import React, { useEffect } from 'react';
import {
  useMediaQuery,
  Box,
  Stack,
  Icon,
  Text,
  IconButton,
  Flex,
  useDisclosure,
  Avatar,
  HStack,
  ModalContent,
  ModalHeader,
  ModalBody,
  Divider,
} from '@chakra-ui/react';
import { FiChevronLeft } from 'react-icons/fi';
import { useHistory } from 'react-router-dom';
import { GlobalHotKeys } from 'react-hotkeys';
import { isIOS } from 'react-device-detect';

import auth from 'modules/auth';
import api, { ResponseApi } from 'services/api';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { useFullScreenContext } from 'store/FullScreen';
import ConstanteRotasPDV from 'constants/rotasPDV';
import ConstanteRotas from 'constants/rotas';
import { useSignalRContext } from 'store/SignalR';
import { useMenuContext } from 'store/PDV/Menu';
import { useAtendimentoChatStore } from 'store/Chat';

import {
  ExpandirFullscreenIcon,
  ExpandirFullscreenFomerIcon,
  RecolherFullscreenFomerIcon,
  RecolherFullscreenIcon,
  LogoutIcon,
  AlternarLojaIcon,
  AlternarLojaFomerIcon,
  SuporteTecnicoIcon,
  TeclasAtalhoIcon,
  VoltarRetaguardaIcon,
} from 'icons';
import { PDVHeaderLogo } from 'icons/Identidade';
import ModalAlternarLoja from 'components/PDV/Modal/ModalAlternarLoja';
import ModalAtalhos from 'components/PDV/Modal/ModalAtalhos';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import { ModalAjuda } from 'components/v2/PDV/ModalAjuda';

import { MenuAtalhos } from './MenuAtalhos';
import { MenuHamburguerMobile } from './MenuHamburguerMobile';
import WrapperButton from './WrapperButton';

const Header: React.FC = () => {
  const { menuItems } = useMenuContext();
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const isPdv = window?.location?.pathname?.includes('pdv');
  const iconRecolher = isPdv
    ? RecolherFullscreenFomerIcon
    : RecolherFullscreenIcon;
  const iconExpandir = isPdv
    ? ExpandirFullscreenFomerIcon
    : ExpandirFullscreenIcon;

  const versaoMobile = !isLargerThan900;

  const { handleFullScreen } = useFullScreenContext();

  const history = useHistory();

  const { hubConnection } = useSignalRContext();
  const { abrirAbaChat } = useAtendimentoChatStore();

  const { fantasia: lojaFantasia = '' } = auth.getLoja();
  const { foto: fotoUsuario } = auth.getUsuario();

  const {
    isOpen: isMenuUsuarioWrapperOpen,
    onOpen: onMenuUsuarioWrapperOpen,
    onClose: onMenuUsuarioWrapperClose,
  } = useDisclosure();

  const {
    isOpen: isModalAlternarLojaOpen,
    onOpen: onModalAlternarLojaOpen,
    onClose: onModalAlternarLojaClose,
  } = useDisclosure();

  const {
    isOpen: isModalAtalhosOpen,
    onOpen: onModalAtalhosOpen,
    onClose: onModalAtalhosClose,
  } = useDisclosure();

  async function logOff() {
    await api.post<void, ResponseApi>(
      ConstanteEnderecoWebservice.AUTENTICACAO_LOGOFF
    );

    auth.clearTokenAndRedirect();
  }

  function handleVoltarParaDashboard() {
    history.push(ConstanteRotas.DASHBOARD, null);
  }

  function handlePushCentralAjuda() {
    ModalAjuda({
      abrirChat: abrirAbaChat,
    });
  }

  function toggleFullScreen() {
    if (!handleFullScreen.active) {
      handleFullScreen.enter();
    } else {
      handleFullScreen.exit();
    }
  }

  const keyMap = {
    F1: ['f1'],
    F11: ['f11'],
  };

  const handlersHotKeys = {
    F1: (event: any) => {
      event.preventDefault();
      onModalAtalhosOpen();
    },
    F11: (event: any) => {
      event.preventDefault();
      toggleFullScreen();
    },
  };

  useEffect(() => {
    hubConnection.on('trocar-loja', () => {
      const { pathname } = window.location;

      if (pathname.indexOf('pdv') !== -1) {
        history.push(ConstanteRotasPDV.PDV_HOME, null);
      } else {
        history.push(ConstanteRotas.DASHBOARD, null);
      }
      history.go(0);
    });
  }, [history, hubConnection]);

  return (
    <>
      <GlobalHotKeys handlers={handlersHotKeys} keyMap={keyMap} />
      <Box bg="pdv.header" w="100%" h={{ base: '50px', md: '40px' }} px={6}>
        <Stack
          direction="row"
          align="center"
          justify="space-between"
          spacing={2}
          h="100%"
        >
          <HStack spacing="5">
            {versaoMobile && (menuItems || []).length > 0 && (
              <MenuHamburguerMobile menuItems={menuItems} />
            )}

            <PDVHeaderLogo />
          </HStack>

          <Stack direction="row" align="center" gap="0px" spacing="0px">
            {isLargerThan900 && <MenuAtalhos />}
            {versaoMobile && (
              <>
                <Avatar
                  src={fotoUsuario}
                  size="xs"
                  cursor="pointer"
                  onClick={onMenuUsuarioWrapperOpen}
                  border="1px"
                  borderColor="primary.50"
                />

                <ModalPadraoChakra
                  motionPreset="none"
                  size="full"
                  onClose={onMenuUsuarioWrapperClose}
                  isOpen={isMenuUsuarioWrapperOpen}
                >
                  <ModalContent
                    margin="0"
                    borderRadius="0"
                    overflowX="hidden"
                    overflowY="auto"
                    h="full"
                    bg="pdv.header"
                  >
                    <ModalHeader p="0">
                      <Flex
                        h="40px"
                        alignItems="center"
                        justifyContent="space-between"
                        pl="5"
                        pr="6"
                      >
                        <IconButton
                          aria-label="Voltar"
                          icon={<Icon as={FiChevronLeft} boxSize="6" />}
                          variant="link"
                          color="pdv.headerTextColor"
                          onClick={onMenuUsuarioWrapperClose}
                          minW="40px"
                          height="40px"
                          borderRadius="0px"
                          _focus={{
                            outline: 'none',
                            backgroundColor: '#ffffff60',
                          }}
                        />

                        <Text color="pdv.headerTextColor" fontSize="sm" mr="2">
                          {lojaFantasia}
                        </Text>
                      </Flex>
                    </ModalHeader>
                    <ModalBody
                      py="0"
                      px="5"
                      sx={{
                        '& > *': { borderColor: 'pdv.modalUsuarioDivider' },
                      }}
                    >
                      <WrapperButton
                        icon={SuporteTecnicoIcon}
                        title="Central de ajuda"
                        onClick={handlePushCentralAjuda}
                        hasArrow
                      />
                      <WrapperButton
                        icon={TeclasAtalhoIcon}
                        title="Atalhos"
                        onClick={onModalAtalhosOpen}
                        hasArrow
                      />
                      {!isIOS && (
                        <WrapperButton
                          icon={
                            handleFullScreen.active
                              ? iconRecolher
                              : iconExpandir
                          }
                          title={
                            handleFullScreen.active
                              ? 'Sair do modo tela cheia'
                              : 'Maximizar tela'
                          }
                          onClick={toggleFullScreen}
                        />
                      )}

                      <Divider
                        w="calc(100% - 24px)"
                        borderColor="pdv.modalUsuarioDivider"
                        my="12px"
                        ml="10px"
                      />
                      <WrapperButton
                        icon={VoltarRetaguardaIcon}
                        title="Ir para retaguarda"
                        onClick={handleVoltarParaDashboard}
                      />
                      <WrapperButton
                        icon={isPdv ? AlternarLojaFomerIcon : AlternarLojaIcon}
                        title="Alternar Loja"
                        onClick={onModalAlternarLojaOpen}
                        hasArrow
                      />
                      <WrapperButton
                        icon={LogoutIcon}
                        title="Sair do sistema"
                        onClick={() => logOff()}
                      />
                    </ModalBody>
                  </ModalContent>
                </ModalPadraoChakra>
              </>
            )}
            <ModalAlternarLoja
              isOpen={isModalAlternarLojaOpen}
              onClose={onModalAlternarLojaClose}
              onSubmit={() => {}}
              subTittle="Ao trocar a loja você será redirecionado para a tela inicial do PDV"
              asMobileView={!isLargerThan900}
            />
            <ModalAtalhos
              isOpen={isModalAtalhosOpen}
              onClose={onModalAtalhosClose}
              asMobileView={!isLargerThan900}
            />
          </Stack>
        </Stack>
      </Box>
    </>
  );
};

export default Header;
