import { Box, Flex, Text, Divider, useColorModeValue } from '@chakra-ui/react';
import React from 'react';

import { DecimalMask } from 'helpers/format/fieldsMasks';

interface TotalizadoresFixosProps {
  quantidadeItens: number;
  totalProdutos: number;
  valorTotalProdutos: number;
}

const TotalizadoresFixos: React.FC<TotalizadoresFixosProps> = ({
  quantidadeItens,
  totalProdutos,
  valorTotalProdutos,
}) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const highlightColor = useColorModeValue('blue.600', 'blue.300');

  return (
    <Box
      position="fixed"
      bottom="0"
      left="0"
      right="0"
      bg={bgColor}
      borderTop={`2px solid ${borderColor}`}
      boxShadow="0 -4px 12px rgba(0, 0, 0, 0.1)"
      zIndex={1000}
      p={4}
    >
      <Flex
        direction={{ base: 'column', md: 'row' }}
        gap={6}
        maxW="1200px"
        mx="auto"
        align="center"
        justify="space-between"
      >
        <Flex gap={6} align="center">
          <Box>
            <Text fontSize="xs" color={textColor} fontWeight="medium">
              Total de produtos
            </Text>
            <Text fontSize="sm" fontWeight="bold" color={highlightColor}>
              {totalProdutos.toLocaleString('pt-BR')}
            </Text>
          </Box>

          <Divider orientation="vertical" h="6" borderColor={borderColor} />

          <Box>
            <Text fontSize="xs" color={textColor} fontWeight="medium">
              Quantidade de itens
            </Text>
            <Text fontSize="sm" fontWeight="bold" color={highlightColor}>
              {quantidadeItens.toLocaleString('pt-BR')}
            </Text>
          </Box>
        </Flex>

        <Box textAlign="right">
          <Text fontSize="xs" color={textColor} fontWeight="medium" mb={1}>
            Valor total de produtos
          </Text>
          <Text fontSize="lg" fontWeight="bold" color={highlightColor}>
            R$ {DecimalMask(valorTotalProdutos, 2, 2)}
          </Text>
        </Box>
      </Flex>
    </Box>
  );
};

export default TotalizadoresFixos;
