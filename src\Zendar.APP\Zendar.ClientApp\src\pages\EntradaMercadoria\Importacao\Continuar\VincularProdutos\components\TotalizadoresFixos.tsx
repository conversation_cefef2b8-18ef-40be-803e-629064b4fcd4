import { Box, Flex, Text, Divider, useColorModeValue } from '@chakra-ui/react';
import React from 'react';

import { DecimalMask } from 'helpers/format/fieldsMasks';

interface TotalizadoresFixosProps {
  quantidadeItens: number;
  totalProdutos: number;
  valorTotalProdutos: number;
}

const TotalizadoresFixos: React.FC<TotalizadoresFixosProps> = ({
  quantidadeItens,
  totalProdutos,
  valorTotalProdutos,
}) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const highlightColor = useColorModeValue('blue.600', 'blue.300');

  return (
    <Box
      position="fixed"
      bottom="0"
      left="0"
      right="0"
      bg={bgColor}
      borderTop={`2px solid ${borderColor}`}
      boxShadow="0 -4px 12px rgba(0, 0, 0, 0.1)"
      zIndex={1000}
      py={3}
      px={6}
      h="80px" // Altura fixa menor
    >
      <Flex
        direction={{ base: 'column', md: 'row' }}
        gap={4}
        maxW="1200px"
        mx="auto"
        align="center"
        justify="space-between"
        h="full"
      >
        <Flex gap={4} align="center">
          <Box textAlign="center">
            <Text fontSize="xs" color={textColor} fontWeight="medium">
              Total de produtos
            </Text>
            <Text fontSize="sm" fontWeight="bold" color={highlightColor}>
              {totalProdutos.toLocaleString('pt-BR')}
            </Text>
          </Box>

          <Divider orientation="vertical" h="8" borderColor={borderColor} />

          <Box textAlign="center">
            <Text fontSize="xs" color={textColor} fontWeight="medium">
              Quantidade de itens
            </Text>
            <Text fontSize="sm" fontWeight="bold" color={highlightColor}>
              {quantidadeItens.toLocaleString('pt-BR')}
            </Text>
          </Box>
        </Flex>

        <Box textAlign="right">
          <Text fontSize="xs" color={textColor} fontWeight="medium">
            Valor total de produtos
          </Text>
          <Text fontSize="md" fontWeight="bold" color={highlightColor}>
            R$ {DecimalMask(valorTotalProdutos, 2, 2)}
          </Text>
        </Box>
      </Flex>
    </Box>
  );
};

export default TotalizadoresFixos;
