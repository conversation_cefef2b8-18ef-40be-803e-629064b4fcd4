import { FormProvider, useForm } from 'react-hook-form';
import { SetStateAction, useCallback, useState } from 'react';
import {
  ModalContent,
  ModalProps as ModalChakraProps,
  useDisclosure,
  Text,
  VStack,
  ModalBody,
  ModalFooter,
  Button,
  GridItem,
  useMediaQuery,
  Icon,
  FormLabel,
  Flex,
  Box,
  Divider,
  Tooltip,
} from '@chakra-ui/react';

import { toast } from 'react-toastify';
import { create, InstanceProps } from 'react-modal-promise';

import api, { ResponseApi } from 'services/api';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import enumTipoItemProdutoEtapa from 'constants/enum/enumTipoItemProdutoEtapa';

import { InfoIconCircular } from 'icons';
import { Switch } from 'components/update/Switch';
import Input from 'components/PDV/Input';
import ModalPadraoChakra from 'components/PDV/Modal/ModalPadraoChakra';
import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import SelectField from 'components/PDV/Select/SelectPadrao';
import { SelectMulti } from 'components/Select/SelectMultiCheckbox';
import AsyncSelectField from 'components/PDV/Select/AsyncSelectPadrao';

import { OptionProps } from 'types/optionType';

import {
  FormData,
  yupResolver,
  defaultValues,
  CategoryProps,
  ProductProps,
  VALOR_CAMPO_VAZIO,
  telasExibicaoNome,
} from './validationForm';
import ListProductsVirtualized from '../../listProductsVirtualized';
import { BotaoAlternarCampoVazio } from 'pages/Produtos/components/EdicaoEmMassa/BotaoAlternarCampoVazio';
import { obterEstiloInputDesabilitado } from 'pages/Produtos/components/EdicaoEmMassa/ExibirCampo/estilos';

type ModalProps = {
  title?: string;
  callback: (data: FormData) => Promise<{ success: boolean }>;
};

type ModalEtapaCadastrarProps = Omit<
  ModalChakraProps,
  'isOpen' | 'onClose' | 'children'
> &
  InstanceProps<void> &
  ModalProps;

const ModalEtapaCadastrar = create<ModalEtapaCadastrarProps>(
  ({ title = 'Adicionar etapa', callback, ...rest }) => {
    const [isSmallerThan900] = useMediaQuery('(max-width: 900px)');
    const [isSmallerThan1200] = useMediaQuery('(max-width: 1200px)');
    const { isOpen, onClose } = useDisclosure({ defaultIsOpen: true });

    const formMethods = useForm({
      resolver: yupResolver,
      defaultValues,
    });

    const { handleSubmit, reset, watch, setError, setValue, clearErrors } =
      formMethods;
    const { tipoEtapa, permitirItensExtras, telasExibicao } = watch();

    const tipoEtapaProduto = tipoEtapa === enumTipoItemProdutoEtapa.PRODUTO;

    const [isLoading, setIsLoading] = useState(false);
    const [productsIsLoading, setProductsIsLoading] = useState(false);
    const [listaProdutos, setListaProdutos] = useState<ProductProps[]>([]);

    const [campoEscolhasPorItemEstaVazio, setCampoEscolhasPorItemEstaVazio] =
      useState(true);
    const [campoQuantidadeMinimaEstaVazio, setCampoQuantidadeMinimaEstaVazio] =
      useState(true);
    const [campoQuantidadeMaximaEstaVazio, setCampoQuantidadeMaximaEstaVazio] =
      useState(true);

    const naoPossuiTelaExibicaoSelecionada = telasExibicao?.length === 0;

    const estiloInputDesabilitado = obterEstiloInputDesabilitado();

    const hadleAlternarCampoVazio = (
      name:
        | 'quantidadeMinima'
        | 'quantidadeMaxima'
        | 'quantidadeEscolhasPorItem',
      setCampoVazio: (value: SetStateAction<boolean>) => void
    ) => {
      setValue(name, '');
      setCampoVazio((prev) => !prev);
    };

    const cadastrarEtapa = useCallback(
      async (data: FormData) => {
        const itens = tipoEtapaProduto
          ? listaProdutos
              .filter(({ isSelected }) => isSelected)
              .map(({ id }) => id)
          : [];

        const { success } = await callback({
          ...data,
          itens,
        });

        return { success };
      },
      [callback, tipoEtapaProduto, listaProdutos]
    );

    const handleConfirmaSair = async (data: FormData) => {
      if (naoPossuiTelaExibicaoSelecionada) return;

      setIsLoading(true);

      const { success } = await cadastrarEtapa(data);

      if (success) {
        onClose();
      }

      setIsLoading(false);
    };

    const handleConfirmarResetar = async (data: FormData) => {
      if (naoPossuiTelaExibicaoSelecionada) return;
      setIsLoading(true);

      const { success } = await cadastrarEtapa(data);

      if (success) {
        setListaProdutos([]);
        reset();
      }

      setIsLoading(false);
    };

    const onChangeCategoria = useCallback(async (option: OptionProps) => {
      if (!option) {
        return;
      }

      setProductsIsLoading(true);

      const response = await api.get<void, ResponseApi<ProductProps[]>>(
        ConstanteEnderecoWebservice.PRODUTO_ETAPA_PRODUTOS_CATEGORIA_OBTER.replace(
          'id',
          option.value as string
        )
      );

      if (response) {
        if (response?.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }

        if (response?.sucesso && response?.dados) {
          const newProducts = response.dados.map((product) => ({
            ...product,
            isSelected: false,
          }));

          setListaProdutos(newProducts);
        }
      }

      setProductsIsLoading(false);
    }, []);

    const buscarListaCategorias = useCallback(async (inputValue: string) => {
      const response = await api.get<void, ResponseApi<CategoryProps[]>>(
        ConstanteEnderecoWebservice.PRODUTO_ETAPA_CATEGORIAS_OBTER,
        {
          params: { pesquisa: inputValue },
        }
      );

      if (response) {
        if (response?.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }

        if (response?.sucesso && response?.dados) {
          return response.dados.map((category) => ({
            label: category.nome,
            value: category.id,
          }));
        }
      }

      return [];
    }, []);

    const handleSetListProducts = (products: ProductProps[]) => {
      setListaProdutos(products);
    };

    const handleChangeInputsQuantidade = (
      name:
        | 'quantidadeMinima'
        | 'quantidadeEscolhasPorItem'
        | 'quantidadeMaxima',
      e: any
    ) => {
      if (e.target.value > 999) {
        setValue(name, '999');
      } else {
        const novoValor = (e.target.value as string).replace(/[^0-9]/g, '');
        setValue(name, novoValor);
      }
    };

    const validarQuantidadeMinimaMaxima = (
      quantidadeMinima: number,
      quantidadeMaxima: number
    ) => {
      if (
        quantidadeMaxima > 0 &&
        quantidadeMinima > 0 &&
        quantidadeMaxima < quantidadeMinima
      ) {
        toast.warn('A quantidade mínima não pode ser maior que a máxima');

        setError(
          'quantidadeMaxima',
          {
            message: ' ',
          },
          { shouldFocus: false }
        );
        return false;
      }
      return true;
    };

    return (
      <ModalPadraoChakra
        isCentered={!isSmallerThan1200}
        size={!isSmallerThan900 ? '6xl' : 'full'}
        {...rest}
        isOpen={isOpen}
        onClose={onClose}
      >
        <ModalContent px="0px" bg="gray.50" w="1080px">
          {isLoading && <LoadingPadrao />}

          <ModalBody p="0px">
            <FormProvider {...formMethods}>
              <Flex flexDir={['column', 'row']}>
                <SimpleGridForm
                  w="full"
                  gap={{ base: 6, md: 8 }}
                  pl="40px"
                  pr="24px"
                  py="32px"
                >
                  <GridItem colSpan={12}>
                    <VStack
                      alignItems="flex-start"
                      spacing="1"
                      lineHeight="1"
                      gap="16px"
                    >
                      <Text
                        color="primary.50"
                        fontSize="16px"
                        fontWeight="semibold"
                      >
                        {title}
                      </Text>
                      <Divider />
                    </VStack>
                  </GridItem>

                  <GridItem colSpan={{ base: 12, lg: 6 }}>
                    <SelectField
                      id="tipoEtapa"
                      name="tipoEtapa"
                      label="Tipo de configuração"
                      placeholder="Selecione"
                      options={enumTipoItemProdutoEtapa.options}
                      isDisabled={isLoading}
                    />
                  </GridItem>
                  <GridItem colSpan={{ base: 12, lg: 6 }}>
                    <AsyncSelectField
                      id="categoriaProdutoId"
                      name="categoriaProdutoId"
                      label="Categoria"
                      placeholder="Selecione uma categoria"
                      handleGetOptions={buscarListaCategorias}
                      onOptionSelect={onChangeCategoria}
                      isDisabled={isLoading}
                    />
                  </GridItem>
                  <GridItem colSpan={{ base: 12, lg: 6 }}>
                    <SelectMulti
                      textLabelSelectAll="Todos aplicativos do sistema"
                      id="telasExibicao"
                      name="telasExibicao"
                      label="Onde exibir?"
                      placeholder="Selecione uma opção"
                      variant="outline"
                      errorText={
                        naoPossuiTelaExibicaoSelecionada
                          ? 'Selecione ao menos uma opção'
                          : ''
                      }
                      options={telasExibicaoNome}
                      isMulti
                      isSearchable={false}
                      closeMenuOnSelect={false}
                    />
                  </GridItem>
                  <GridItem colSpan={{ base: 12, lg: 6 }}>
                    <Input
                      id="textoParaExibicao"
                      name="textoParaExibicao"
                      placeholder='Ex: "Vai um recheio extra?"'
                      label="Texto de exibição na tela"
                      maxLength={28}
                    />
                  </GridItem>
                  <GridItem colSpan={12}>
                    <Text fontWeight="bold" fontSize="14px">
                      Quantidade de escolhas permitidas na etapa:
                      <Tooltip
                        hasArrow
                        bg="black"
                        placement="bottom-start"
                        label={
                          <Flex flexWrap="wrap" fontSize="12px">
                            <Text fontWeight="bold" as="span">
                              Quantidade mínima e máxima:
                              <Text
                                fontWeight="normal"
                                as="span"
                                letterSpacing="0px"
                              >
                                {' '}
                                Informe a quantidade limite de escolhas que o
                                usuário pode fazer na etapa. Se for permitido
                                pular a etapa sem fazer escolhas, clique no
                                ícone para desconsiderar a quantidade mínima.
                              </Text>
                            </Text>

                            <Text mt="24px" letterSpacing="0px">
                              Utilize o campo “limitar a quantidade de escolhas
                              por item” apenas se preferir bloquear o usuário de
                              escolher um número repetido do mesmo ítem. (Ex:
                              Não permitir que o usuário selecione 2 vezes o
                              mesmo tipo de pão)
                            </Text>
                          </Flex>
                        }
                        p="20px"
                        borderRadius="6px"
                        tabIndex={-1}
                        maxW="344px"
                      >
                        <Box tabIndex={-1} as="span" ml="4px">
                          <Icon as={InfoIconCircular} w="15px" h="15px" />
                        </Box>
                      </Tooltip>
                    </Text>
                    <SimpleGridForm
                      bg="gray.100"
                      borderColor="gray.200"
                      borderWidth="1px"
                      borderRadius="5px"
                      padding="0px 40px"
                      gap="10px"
                    >
                      <GridItem colSpan={{ base: 12, md: 12, lg: 7 }} py="24px">
                        <Flex
                          align="center"
                          flexWrap={['wrap', 'wrap', 'nowrap']}
                        >
                          <FormLabel minW="135px" mt="6px">
                            Quantidade Mínima
                          </FormLabel>
                          <Box mr="2px">
                            <Input
                              id="quantidadeMinima"
                              name="quantidadeMinima"
                              placeholder="Ex: 1"
                              textAlign="left"
                              pl="12px"
                              maxW="120px"
                              value={
                                campoQuantidadeMinimaEstaVazio
                                  ? VALOR_CAMPO_VAZIO
                                  : undefined
                              }
                              _disabled={estiloInputDesabilitado}
                              isDisabled={campoQuantidadeMinimaEstaVazio}
                              onChangeCapture={(e: any) => {
                                const quantidadeMinima = Number(e.target.value);
                                const quantidadeMaxima = Number(
                                  watch('quantidadeMaxima')
                                );

                                const passouValidacao =
                                  validarQuantidadeMinimaMaxima(
                                    quantidadeMinima,
                                    quantidadeMaxima
                                  );

                                if (!passouValidacao) return;
                                clearErrors('quantidadeMaxima');

                                handleChangeInputsQuantidade(
                                  'quantidadeMinima',
                                  e
                                );
                              }}
                            />
                          </Box>
                          <BotaoAlternarCampoVazio
                            campoEstaVazio={campoQuantidadeMinimaEstaVazio}
                            exibirTooltip={false}
                            bg="white"
                            onClick={() =>
                              hadleAlternarCampoVazio(
                                'quantidadeMinima',
                                setCampoQuantidadeMinimaEstaVazio
                              )
                            }
                          />
                        </Flex>
                        <Flex
                          align="center"
                          mt="8px"
                          flexWrap={['wrap', 'wrap', 'nowrap']}
                        >
                          <FormLabel minW="135px" mt="6px">
                            Quantidade Máxima
                          </FormLabel>
                          <Box mr="2px">
                            <Input
                              id="quantidadeMaxima"
                              name="quantidadeMaxima"
                              placeholder="Ex: 5"
                              textAlign="left"
                              pl="12px"
                              maxW="120px"
                              value={
                                campoQuantidadeMaximaEstaVazio
                                  ? VALOR_CAMPO_VAZIO
                                  : undefined
                              }
                              _disabled={estiloInputDesabilitado}
                              isDisabled={campoQuantidadeMaximaEstaVazio}
                              onChangeCapture={(e: any) => {
                                const quantidadeMaxima = Number(e.target.value);
                                const quantidadeMinima = Number(
                                  watch('quantidadeMinima')
                                );

                                const passouValidacao =
                                  validarQuantidadeMinimaMaxima(
                                    quantidadeMinima,
                                    quantidadeMaxima
                                  );

                                if (!passouValidacao) return;

                                clearErrors('quantidadeMaxima');
                                handleChangeInputsQuantidade(
                                  'quantidadeMaxima',
                                  e
                                );
                              }}
                            />
                          </Box>
                          <BotaoAlternarCampoVazio
                            campoEstaVazio={campoQuantidadeMaximaEstaVazio}
                            exibirTooltip={false}
                            bg="white"
                            onClick={() =>
                              hadleAlternarCampoVazio(
                                'quantidadeMaxima',
                                setCampoQuantidadeMaximaEstaVazio
                              )
                            }
                          />
                        </Flex>
                      </GridItem>
                      {!isSmallerThan1200 && (
                        <GridItem colSpan={1}>
                          <Divider
                            orientation="vertical"
                            borderColor="gray.200"
                          />
                        </GridItem>
                      )}
                      <GridItem colSpan={{ base: 12, lg: 4 }} py="24px">
                        <Flex>
                          <Box w="full">
                            <FormLabel
                              minW="135px"
                              mb="4px"
                              letterSpacing="0px"
                              lineHeight="1.4"
                            >
                              Limitar a quantidade de escolhas por item:
                            </FormLabel>
                            <Flex flexWrap="wrap">
                              <Input
                                id="quantidadeEscolhasPorItem"
                                name="quantidadeEscolhasPorItem"
                                placeholder="Ex: 3"
                                textAlign="left"
                                pl="12px"
                                maxW="120px"
                                mr="2px"
                                value={
                                  campoEscolhasPorItemEstaVazio
                                    ? VALOR_CAMPO_VAZIO
                                    : undefined
                                }
                                _disabled={estiloInputDesabilitado}
                                isDisabled={campoEscolhasPorItemEstaVazio}
                                onChangeCapture={(e) => {
                                  handleChangeInputsQuantidade(
                                    'quantidadeEscolhasPorItem',
                                    e
                                  );
                                }}
                              />{' '}
                              <BotaoAlternarCampoVazio
                                campoEstaVazio={campoEscolhasPorItemEstaVazio}
                                exibirTooltip={false}
                                bg="white"
                                onClick={() =>
                                  hadleAlternarCampoVazio(
                                    'quantidadeEscolhasPorItem',
                                    setCampoEscolhasPorItemEstaVazio
                                  )
                                }
                              />
                            </Flex>
                          </Box>
                        </Flex>
                      </GridItem>
                    </SimpleGridForm>
                  </GridItem>
                  <GridItem colSpan={12}>
                    <Flex>
                      <Box mr="16px" mt="12px">
                        <Switch
                          size="md"
                          label=""
                          colorScheme="blue"
                          id="permitirItensExtras"
                          name="permitirItensExtras"
                        />
                      </Box>
                      <Flex flexDir="column">
                        <FormLabel
                          mb="0px"
                          htmlFor="permitirItensExtras"
                          userSelect="none"
                          cursor="pointer"
                        >
                          Permitir que o usuário adicione escolhas acima da
                          quantidade máxima.
                        </FormLabel>
                        <FormLabel
                          fontSize="14px"
                          fontStyle="italic"
                          fontWeight="normal"
                          letterSpacing="0px"
                          htmlFor="permitirItensExtras"
                          userSelect="none"
                          cursor="pointer"
                          color={permitirItensExtras ? 'blue.600' : 'black'}
                        >
                          (as escolhas serão consideradas “extras” e serão
                          acrescentadas ao valor total)
                        </FormLabel>
                      </Flex>
                    </Flex>
                  </GridItem>
                </SimpleGridForm>
                <SimpleGridForm bg="gray.100" px="24px" py="32px">
                  {tipoEtapaProduto && listaProdutos?.length > 0 && (
                    <GridItem colSpan={12}>
                      <FormLabel mb="0px" fontSize="14px">
                        Selecione os produtos
                      </FormLabel>
                      <ListProductsVirtualized
                        listProducts={listaProdutos}
                        setListProducts={handleSetListProducts}
                        isLoading={productsIsLoading}
                      />
                    </GridItem>
                  )}
                </SimpleGridForm>
              </Flex>
            </FormProvider>
          </ModalBody>
          <ModalFooter
            justifyContent="center"
            borderTop="1px solid"
            borderColor="gray.200"
            flexWrap="wrap"
            gap={6}
            py="32px"
          >
            <Button
              w={{ base: 'full', sm: '160px' }}
              colorScheme="gray"
              h="32px"
              fontSize="14px"
              variant="outlineDefault"
              onClick={onClose}
            >
              Cancelar
            </Button>
            <Button
              w={{ base: 'full', sm: '160px' }}
              colorScheme="gray"
              h="32px"
              fontSize="14px"
              variant="outlineDefault"
              onClick={handleSubmit(handleConfirmaSair)}
              isDisabled={isLoading || productsIsLoading}
            >
              Confirmar e sair
            </Button>
            <Button
              w={{ base: 'full', sm: '320px' }}
              minW="225px"
              h="32px"
              fontSize="14px"
              colorScheme="secondary"
              onClick={handleSubmit(handleConfirmarResetar)}
              isDisabled={isLoading || productsIsLoading}
            >
              Confirmar e adicionar outra etapa
            </Button>
          </ModalFooter>
        </ModalContent>
      </ModalPadraoChakra>
    );
  }
);

export default ModalEtapaCadastrar;
