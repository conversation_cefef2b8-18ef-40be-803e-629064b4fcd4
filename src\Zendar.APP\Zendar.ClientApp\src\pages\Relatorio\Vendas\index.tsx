import { <PERSON>, But<PERSON>, Flex, FormLabel, GridItem, Text } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import InputDateRange from 'components/PDV/InputDateRange';
import AsyncSelect from 'components/PDV/Select/AsyncSelectPadrao';
import SelectPadrao from 'components/PDV/Select/SelectPadrao';
import { SelectMulti } from 'components/Select/SelectMultiCheckbox';
import { opcoesTipoFiscal } from 'components/update/CustomFormStandard/constants';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import { Switch } from 'components/update/Switch';
import InputDateOrTime from 'components/v2/ui/InputDateOrTime';

import { IdentificacaoIntegracaoOptions } from 'constants/enum/IdentificacaoIntegracao';
import { statusOperacaoOptions as listStatus } from 'constants/enum/statusOperacao';
import { IconesCadastroPromocao } from 'icons';

import { useRelatorioVendas } from './hooks';
import { formDefaultValues, FormData, yupResolver } from './validationForms';

export const RelatorioVendas = () => {
  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: formDefaultValues,
  });

  const {
    isLoading,
    isRelatorioVendaSimplificada,
    listaTipoRelatoriosFiltradosServicos,
    getClientes,
    listaVendedores,
    possuiServicoFrenteCaixa,
    naoExibirCampoEntregador,
    buscarListaEntregadores,
    obterOpcoesLocaisDeEstoque,
    handleGerarRelatorio,
    inclusoHorasNoPeriodo,
    handleFormatarDatas,
    resetarErrosInputsDataHora,
  } = useRelatorioVendas(formMethods);

  return (
    <FormProvider {...formMethods}>
      {isLoading && <LoadingPadrao />}
      <SimpleGridForm
        bg="white"
        pt={['20px', '20px', '40px']}
        pl={['20px', '20px', '40px']}
        pb={['20px', '20px', '40px']}
        pr={['20px', '20px', '40px']}
        borderRadius="5px"
        boxShadow="0px 0px 6px #00000034"
      >
        <GridItem
          colSpan={{
            base: 12,
            sm: inclusoHorasNoPeriodo ? 12 : 6,
            lg: isRelatorioVendaSimplificada ? 3 : 6,
          }}
        >
          <SelectPadrao
            placeholder="Selecione um relatório"
            id="tipoRelatorio"
            name="tipoRelatorio"
            required
            label="Relatório"
            options={listaTipoRelatoriosFiltradosServicos}
          />
        </GridItem>
        <GridItem
          colSpan={{
            base: 12,
            sm: inclusoHorasNoPeriodo ? 12 : 6,
            lg: !inclusoHorasNoPeriodo ? 3 : 6,
          }}
        >
          <Flex w="full" wrap="wrap">
            <FormLabel mb="0" fontSize="sm" color="black" lineHeight="1.2">
              Período *
            </FormLabel>
            <Flex
              margin="0 0 0 auto"
              gap="12px"
              height="16px"
              align="flex-start"
              mb="2px"
            >
              <Box>
                <Switch
                  size="sm"
                  colorScheme="blue"
                  id="inclusoHorasNoPeriodo"
                  name="inclusoHorasNoPeriodo"
                  onChange={(e) => {
                    const incluirHoras = e?.target?.checked;
                    resetarErrosInputsDataHora();
                    handleFormatarDatas({
                      incluirHoras,
                    });
                  }}
                  sx={{
                    '.chakra-switch__track': {
                      boxShadow: 'none !important',
                    },
                  }}
                />
              </Box>
              <FormLabel
                fontSize="xs"
                color="gray.700"
                userSelect="none"
                cursor="pointer"
                mr="0px"
                htmlFor="inclusoHorasNoPeriodo"
              >
                Incluir horas no filtro
              </FormLabel>
            </Flex>
          </Flex>
          {inclusoHorasNoPeriodo ? (
            <Flex
              gap={{ base: 2 }}
              direction={{ base: 'column', sm: 'row' }}
              align="center"
            >
              <InputDateOrTime
                type="datetime-local"
                name="dataEmissaoInicio"
                icon={IconesCadastroPromocao.Calendario}
              />
              <Text display={{ base: 'none', sm: 'block' }}>até</Text>
              <InputDateOrTime
                type="datetime-local"
                name="dataEmissaoFim"
                icon={IconesCadastroPromocao.Calendario}
              />
            </Flex>
          ) : (
            <InputDateRange
              borderColor="gray.100"
              borderRadius="md"
              placeholder="dd/mm/aaaa"
              startDateName="dataEmissaoInicio"
              endDateName="dataEmissaoFim"
              maxDate={new Date()}
              inputValueIsSmaller={false}
            />
          )}
        </GridItem>
        <GridItem
          colSpan={{
            base: 12,
            sm: 6,
            lg: 3,
          }}
        >
          <AsyncSelect
            id="clienteFornecedorId"
            name="clienteFornecedorId"
            placeholder="Informe o cliente"
            label="Cliente"
            handleGetOptions={getClientes}
            asControlledByObject
            isClearable
            shouldAppearTheAddress
          />
        </GridItem>
        <GridItem colSpan={{ base: 12, sm: 6, lg: 3 }}>
          <SelectPadrao
            id="statusConsulta"
            name="statusConsulta"
            label="Status"
            isSearchable={false}
            options={listStatus}
          />
        </GridItem>
        <GridItem colSpan={{ base: 12, sm: 6, lg: 3 }}>
          <SelectPadrao
            id="vendedorId"
            name="vendedorId"
            label="Vendedor"
            isClearable
            placeholder="Selecione um vendedor"
            options={listaVendedores}
          />
        </GridItem>
        {possuiServicoFrenteCaixa && !naoExibirCampoEntregador && (
          <GridItem colSpan={{ base: 12, sm: 6, lg: 3 }}>
            <AsyncSelect
              id="entregadorId"
              name="entregadorId"
              label="Entregador"
              isClearable
              isSearchable
              asControlledByObject
              handleGetOptions={buscarListaEntregadores}
              placeholder="Informe o entregador"
            />
          </GridItem>
        )}
        <GridItem colSpan={{ base: 12, sm: 6, lg: 3 }}>
          <SelectMulti
            id="tipoFiscal"
            name="tipoFiscal"
            label="Tipo fiscal"
            textLabelSelectAll="Todos"
            placeholder="Selecione as opções desejadas"
            options={opcoesTipoFiscal}
            isSearchable={false}
          />
        </GridItem>
        <GridItem colSpan={{ base: 12, sm: 6, lg: 3 }}>
          <AsyncSelect
            id="localEstoqueId"
            name="localEstoqueId"
            label="Local de estoque"
            placeholder="Selecione o local de estoque"
            handleGetOptions={obterOpcoesLocaisDeEstoque}
            isClearable
          />
        </GridItem>
        <GridItem colSpan={{ base: 12, sm: 6, lg: 3 }}>
          <SelectPadrao
            label="Origem"
            id="origem"
            isSearchable={false}
            name="origem"
            options={IdentificacaoIntegracaoOptions}
          />
        </GridItem>
      </SimpleGridForm>
      <Flex
        mt={['20px', '20px', '40px']}
        justifyContent="flex-end"
        alignItems="center"
      >
        <Button
          h="39px"
          color="gray.700"
          colorScheme="secondary"
          borderRadius="md"
          isLoading={isLoading}
          onClick={() => handleGerarRelatorio()}
          width={{ base: 'full', md: '220px' }}
        >
          Gerar relatório
        </Button>
      </Flex>
    </FormProvider>
  );
};
