import React from 'react';
import { <PERSON><PERSON><PERSON>oot<PERSON>, <PERSON><PERSON>, Flex } from '@chakra-ui/react';

import TipoProdutoEnum from 'constants/enum/tipoProduto';

import { EnumTelaConsultaProdutosPdv } from '..';
import { Produto } from 'store/PDV/ConsultaProdutoPdv';
import { VariacoesResponseProps } from '../../VariacaoItem';

interface ModalFooterConsultaProps {
  tela: EnumTelaConsultaProdutosPdv;
  exibirBotaoAdicionarProduto: boolean;
  produtoSelecionado: Produto | null;
  variacaoSelecionada: VariacoesResponseProps | null;
  lancarProdutoDesabilitado: boolean;
  isLargerThan1200: boolean;
  isLargerThan900: boolean;
  isOpenDrawer: boolean;
  isLoading: boolean;
  onReject: () => void;
  onClose: () => void;
  setVariacaoSelecionada: (value: VariacoesResponseProps | null) => void;
  setProdutoSelecionado: (produto: Produto | null) => void;
  setTela: (tela: EnumTelaConsultaProdutosPdv) => void;
  escolherVariacao: (id: string) => void;
  lancarProdutoSimples: (produto: Produto) => void;
  lancarProdutoVariacao: (variacao: VariacoesResponseProps) => void;
}

export const ModalFooterConsulta = ({
  tela,
  exibirBotaoAdicionarProduto,
  produtoSelecionado,
  variacaoSelecionada,
  lancarProdutoDesabilitado,
  isLargerThan1200,
  isLargerThan900,
  isOpenDrawer,
  isLoading,
  onReject,
  onClose,
  setVariacaoSelecionada,
  setProdutoSelecionado,
  setTela,
  escolherVariacao,
  lancarProdutoSimples,
  lancarProdutoVariacao,
}: ModalFooterConsultaProps) => {
  const modalWidth = () => {
    if (isLargerThan1200 && isOpenDrawer && !isLoading)
      return 'calc(100% - 509px)';
    if (isLargerThan900 && isOpenDrawer && !isLoading)
      return 'calc(100% - 448px)';
    return '100%';
  };

  return (
    <ModalFooter
      px="32px"
      py="16px"
      justifyContent={
        exibirBotaoAdicionarProduto ? 'space-between' : 'flex-start'
      }
      borderTop="1px"
      gap="16px"
      transition="right 0.5s ease-in-out, width 0.5s ease-in-out"
      position="fixed"
      bg="gray.50"
      bottom="0"
      w={modalWidth()}
      flexDir={['column', 'column', 'row']}
      borderColor="purple.500"
    >
      <Button
        colorScheme="gray"
        variant="outlineDefault"
        onClick={() => {
          onReject();
          onClose();
        }}
        borderRadius="full"
        minW={isLargerThan1200 && isOpenDrawer ? '96px' : '160px'}
        fontSize={['16px', '16px', '14px']}
        height={['40px', '40px', '32px']}
        fontWeight="600"
        w={['full', 'full', '120px']}
      >
        Fechar
      </Button>

      {tela === EnumTelaConsultaProdutosPdv.PRODUTOS &&
        (produtoSelecionado?.tipoProduto ===
        TipoProdutoEnum.PRODUTO_VARIACAO ? (
          <Button
            colorScheme={lancarProdutoDesabilitado ? 'gray' : 'violet'}
            isDisabled={lancarProdutoDesabilitado}
            borderRadius="full"
            w={['full', 'full', '220px']}
            fontSize={['16px', '16px', '14px']}
            height={['40px', '40px', '32px']}
            margin="0 0 0 auto"
            fontWeight="600"
            onClick={() =>
              produtoSelecionado && escolherVariacao(produtoSelecionado.id)
            }
          >
            Selecionar variação
          </Button>
        ) : (
          exibirBotaoAdicionarProduto && (
            <Button
              colorScheme={lancarProdutoDesabilitado ? 'gray' : 'violet'}
              isDisabled={lancarProdutoDesabilitado}
              borderRadius="full"
              w={['full', 'full', '220px']}
              fontSize={['16px', '16px', '14px']}
              height={['40px', '40px', '32px']}
              fontWeight="600"
              margin="0 0 0 auto"
              onClick={() =>
                produtoSelecionado && lancarProdutoSimples(produtoSelecionado)
              }
            >
              Adicionar produto
            </Button>
          )
        ))}

      {tela === EnumTelaConsultaProdutosPdv.VARIACOES && (
        <Flex
          gap="16px"
          justify="flex-end"
          flexDir={['column', 'column', 'row']}
          w={['full', 'full', 'full']}
        >
          <Button
            colorScheme="gray"
            variant="outlineDefault"
            onClick={() => {
              setVariacaoSelecionada(null);
              setProdutoSelecionado(null);
              setTela(EnumTelaConsultaProdutosPdv.PRODUTOS);
            }}
            borderRadius="full"
            minW="96px"
            fontSize={['16px', '16px', '14px']}
            height={['40px', '40px', '32px']}
            fontWeight="600"
            w={['full', 'full', '96px']}
          >
            Voltar
          </Button>
          {exibirBotaoAdicionarProduto && (
            <Button
              colorScheme={!variacaoSelecionada ? 'gray' : 'violet'}
              isDisabled={!variacaoSelecionada}
              borderRadius="full"
              w={['full', 'full', '220px']}
              fontSize={['16px', '16px', '14px']}
              height={['40px', '40px', '32px']}
              fontWeight="600"
              onClick={() =>
                variacaoSelecionada &&
                lancarProdutoVariacao(variacaoSelecionada)
              }
            >
              Adicionar produto
            </Button>
          )}
        </Flex>
      )}
    </ModalFooter>
  );
};
