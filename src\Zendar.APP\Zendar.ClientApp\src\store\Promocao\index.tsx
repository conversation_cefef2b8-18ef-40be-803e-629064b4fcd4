import React, {
  createContext,
  useState,
  useContext,
  useCallback,
  useEffect,
  useRef,
} from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { toast } from 'react-toastify';
import { useParams } from 'react-router-dom';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import api, { ResponseApi } from 'services/api';
import auth from 'modules/auth';

import { PagedTableForwardRefData } from 'components/update/Table/PagedTable';
import {
  yupResolver,
  defaultValues,
  FormData,
  LojaProps,
} from 'pages/Promocao/Formulario/validationForms';
import { typeCriterioPromocaoQuantidade } from 'pages/Promocao/Formulario/constants';

type PromocaoProviderProps = {
  children: React.ReactNode;
};

type PromocaoContextProps = {
  listaLojas: Array<Omit<LojaProps, 'id'> & { lojaId: string }>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  pagedTableRef: React.RefObject<PagedTableForwardRefData>;
  formIsDirty: boolean;
  setFormIsDirty: React.Dispatch<React.SetStateAction<boolean>>;
  setBloquearAlteracaoTray: React.Dispatch<React.SetStateAction<boolean>>;
  bloquearAlteracaoTray: boolean;
};

export const PromocaoContext = createContext<PromocaoContextProps>(
  {} as PromocaoContextProps
);

type ProdutoCorTamanho = {
  produtoCorTamanhoId: string;
  produto: string;
  cor: string;
  tamanho: string;
  imagem: string;
};

export type ProdutosResponseProps = {
  promocaoId: string;
  produtoCorTamanho: ProdutoCorTamanho;
  precoOriginal: number;
  precoPromocao: number;
  quantidade: number;
  descontoPorQuantidade: boolean;
  criterioPromocaoQuantidade: typeCriterioPromocaoQuantidade;
};

export const PromocaoProvider = ({ children }: PromocaoProviderProps) => {
  const { id: idRota } = useParams<{ id: string }>();
  const pagedTableRef = useRef<PagedTableForwardRefData>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [formIsDirty, setFormIsDirty] = useState(false);
  const [listaLojas, setListaLojas] = useState<
    Array<Omit<LojaProps, 'id'> & { lojaId: string }>
  >([]);
  const [bloquearAlteracaoTray, setBloquearAlteracaoTray] = useState(false);

  const formMethods = useForm<FormData>({
    defaultValues,
    resolver: yupResolver,
  });

  const { setValue } = formMethods;

  const obterLojas = useCallback(async () => {
    setIsLoading(true);

    const response = await api.get<void, ResponseApi<LojaProps[]>>(
      ConstanteEnderecoWebservice.LISTAR_LOJA_ENDERECO
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((item: string) => toast.warning(item));
      }

      if (response?.sucesso) {
        const idLojaAtual = auth.getLoja()?.id;

        const lojas = response.dados.map(({ id, ...loja }) => ({
          ...loja,
          lojaId: id,
        }));

        setListaLojas(lojas);
        setValue(
          'lojas',
          lojas.map((loja) => ({
            ...loja,
            selecionado: loja.lojaId === idLojaAtual,
          }))
        );

        if (!idRota) {
          setIsLoading(false);
        }
      }
    }

    setIsLoading(false);
  }, [idRota, setValue]);

  useEffect(() => {
    obterLojas();
  }, [obterLojas]);

  return (
    <PromocaoContext.Provider
      value={{
        listaLojas,
        isLoading,
        setIsLoading,
        pagedTableRef,
        formIsDirty,
        setFormIsDirty,
        bloquearAlteracaoTray,
        setBloquearAlteracaoTray,
      }}
    >
      <FormProvider {...formMethods}>{children}</FormProvider>
    </PromocaoContext.Provider>
  );
};

export function usePromocaoContext(): PromocaoContextProps {
  const context = useContext(PromocaoContext);

  if (!context)
    throw new Error(
      'usePromocaoContext must be used within a PromocaoProvider.'
    );

  return context;
}
