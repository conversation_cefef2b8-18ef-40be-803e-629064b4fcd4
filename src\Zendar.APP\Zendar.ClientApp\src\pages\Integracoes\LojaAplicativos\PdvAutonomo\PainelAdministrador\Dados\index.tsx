import { VSta<PERSON>, Text, Flex, Button, HStack, <PERSON> } from '@chakra-ui/react';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';

import { Container } from '../components/Container';

import { useDados } from './hooks';

export const Dados = () => {
  const {
    codigoAtivacao,
    isLoading,
    codigoAtivacaoFoiCopiado,
    handleCopiarCodigoAtivacao,
  } = useDados();

  return (
    <>
      {isLoading && <LoadingPadrao />}
      <SimpleGridForm>
        <Container colSpan={[12, 5, 5]} title="Código de ativação">
          <Flex gap="5px">
            {(codigoAtivacao?.listCodigoAtivacao || []).map((code) => (
              <VStack
                alignItems="center"
                justifyContent="center"
                bg="white"
                fontWeight="semibold"
                borderColor="gray.200"
                borderWidth="1px"
                borderStyle="solid"
                color="gray.900"
                fontSize={['12px', '14px', '14px']}
                textAlign="center"
                boxSize="40px"
                ml="5px"
                borderRadius="5px"
              >
                <Text>{code}</Text>
              </VStack>
            ))}
            <Button
              h="2.5rem"
              size="sm"
              onClick={() => handleCopiarCodigoAtivacao()}
              colorScheme={codigoAtivacaoFoiCopiado ? 'secondary' : 'primary'}
              borderRadius="md"
              w="70px"
              autoFocus
            >
              {codigoAtivacaoFoiCopiado ? 'Copiado!' : 'Copiar'}
            </Button>
          </Flex>
        </Container>
        <Container colSpan={[12, 7, 7]} title="Sistema PDV Offline">
          <HStack
            alignItems="center"
            h="full"
            gap="8px"
            justifyContent="center"
            bg="gray.50"
            p="16px"
            w="full"
          >
            <Flex mt="25px" justifyContent="center" w="full">
              <Link
                color="blue.600"
                href="https://sti3cloudstorage.blob.core.windows.net/pdvautonomo/setup/PDVOfflineSetup.exe"
                target="_blank"
              >
                Download do instalador do PDV Offline
              </Link>
            </Flex>
          </HStack>
        </Container>
      </SimpleGridForm>
    </>
  );
};
