import { useCallback, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useMediaQuery } from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';

import api, { ResponseApi } from 'services/api';
import isPrenvent from 'helpers/layout/isPrenvent';
import ConstanteRotas from 'constants/rotas';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { usePromocaoContext } from 'store/Promocao';

import { FormData, defaultValues } from '../validationForms';
import { useFormularioPromocao } from '../hooks';

type PeriodoVigencia = {
  periodoInicio: string;
  periodoFim: string;
};

type HorarioVigencia = {
  periodoInicio: string;
  periodoFim: string;
};

type TabelaPreco = {
  id: string;
  nome: string;
  ativo: boolean;
};

type InformacaoCadastro = {
  ativo: boolean;
  dataHoraCadastro: string;
  dataHoraUltimaAlteracao: string;
};

type PromocaoResponseProps = {
  id: string;
  nome: string;
  periodoVigencia: PeriodoVigencia;
  horarioVigencia: HorarioVigencia;
  diasDaSemana: number[];
  telasUsoPromocao: number[];
  tabelaPreco: TabelaPreco;
  lojasId: string[];
  informacaoCadastro: InformacaoCadastro;
  desconsiderarDias: boolean;
  desconsiderarHorario: boolean;
};

export const usePromocaoAlterar = () => {
  const history = useHistory();
  const { id: idRota } = useParams<{ id: string }>();

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const [dataHoraCadastro, setDataHoraCadastro] = useState('');
  const [dataHoraUltimaAlteracao, setDataHoraUltimaAlteracao] = useState('');

  const { configuracaoPromocao } = useFormularioPromocao();
  const { listaLojas, isLoading, setIsLoading, formIsDirty, setFormIsDirty } =
    usePromocaoContext();

  const {
    handleSubmit,
    formState: { isDirty },
    reset,
  } = useFormContext<FormData>();

  isPrenvent(formIsDirty);

  const formatDate = (date: string) => {
    const currentDate = new Date(date);

    const currentDay = currentDate.getDate();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    return `${currentYear}-${
      currentMonth < 10 ? `0${currentMonth}` : currentMonth
    }-${currentDay < 10 ? `0${currentDay}` : currentDay}`;
  };

  const formatDateHours = (date: string) => {
    const currentDate = new Date(date);

    const currentHours = currentDate.getHours();
    const dateUtc = 3;
    currentDate.setHours(currentHours + dateUtc);
    const hoursDate = `${String(currentDate.getHours()).padStart(
      2,
      '0'
    )}:${String(currentDate.getMinutes()).padStart(2, '0')}`;
    return hoursDate;
  };

  const resetValueFormulario = useCallback(
    (data: PromocaoResponseProps) => {
      const {
        diasDaSemana,
        lojasId,
        telasUsoPromocao,
        nome,
        informacaoCadastro,
        horarioVigencia,
        periodoVigencia,
        desconsiderarDias,
        desconsiderarHorario,
      } = data;

      const listaDiasAtividade = defaultValues.diasAtividade.map((dia) => ({
        ...dia,
        selecionado: diasDaSemana.includes(dia.value),
      }));

      const listaTelasExibicao = defaultValues.telasExibicao.map((tela) => ({
        ...tela,
        selecionado: tela.value ? telasUsoPromocao.includes(tela.value) : false,
      }));

      reset({
        nome,
        ativo: informacaoCadastro.ativo,
        periodoVigenciaInicio: formatDate(periodoVigencia.periodoInicio),
        periodoVigenciaFim: formatDate(periodoVigencia.periodoFim),
        horarioVigenciaFim: formatDateHours(horarioVigencia.periodoFim),
        horarioVigenciaInicio: formatDateHours(horarioVigencia.periodoInicio),
        desconsiderarDias,
        desconsiderarHorario,
        telasExibicao: listaTelasExibicao,
        diasAtividade: listaDiasAtividade,
        lojas: listaLojas.map((loja) => ({
          ...loja,
          selecionado: lojasId.includes(loja.lojaId),
        })),
      });

      setDataHoraUltimaAlteracao(informacaoCadastro.dataHoraUltimaAlteracao);
      setDataHoraCadastro(informacaoCadastro.dataHoraCadastro);
    },
    [listaLojas, reset]
  );

  const obterPromocao = useCallback(async () => {
    if (listaLojas.length === 0) {
      return;
    }

    setIsLoading(true);

    const response = await api.get<void, ResponseApi<PromocaoResponseProps>>(
      `${ConstanteEnderecoWebservice.PROMOCAO}/${idRota}`
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso) {
        resetValueFormulario(response.dados);
      }
    }

    setIsLoading(false);
  }, [idRota, listaLojas, resetValueFormulario, setIsLoading]);

  const onSubmit = handleSubmit(async () => {
    setIsLoading(true);
    setFormIsDirty(false);

    const data = await configuracaoPromocao();

    if (data === false) {
      setIsLoading(false);
      return;
    }

    const response = await api.put<void, ResponseApi<string>>(
      `${ConstanteEnderecoWebservice.PROMOCAO}/${idRota}`,
      {
        ...data,
      }
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((item: string) => toast.warning(item));
      }

      if (response?.sucesso) {
        toast.success('Alteração realizada com sucesso');
        history.push(ConstanteRotas.PROMOCAO);
      }
    }

    setIsLoading(false);
  });

  useEffect(() => {
    setFormIsDirty(isDirty);
  }, [isDirty, setFormIsDirty]);

  useEffect(() => {
    obterPromocao();
  }, [obterPromocao]);

  return {
    isLoading,
    onSubmit,
    formIsDirty,
    dataHoraCadastro,
    dataHoraUltimaAlteracao,
    isLargerThan700,
  };
};
