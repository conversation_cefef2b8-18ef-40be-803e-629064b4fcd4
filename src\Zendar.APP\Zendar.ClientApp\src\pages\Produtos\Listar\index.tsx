import React, {
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import { useHistory } from 'react-router-dom';

import {
  Flex,
  Text,
  Button,
  Icon,
  GridItem,
  Tr,
  Td,
  Link,
  Box,
  Stack,
} from '@chakra-ui/react';
import { toast } from 'react-toastify';
import { FormProvider } from 'react-hook-form';

import auth from 'modules/auth';
import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';
import { enumReferenciaServicoStargate } from 'constants/enum/referenciaServicoStargate';
import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';
import ConstanteRotas, { SubstituirParametroRota } from 'constants/rotas';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import ConstanteFuncionalidades from 'constants/permissoes';
import PlanoContratacaoEnum from 'constants/enum/planoContratacao';
import { useFiltrosProdutoStore } from 'store/Produtos/ListagemProdutos.stores';
import api, { ResponseApi } from 'services/api';
import useIsMountedRef from 'helpers/layout/useIsMountedRef';
import { shallowEqual } from 'helpers/validation/shallowEqual';
import { moneyMask } from 'helpers/format/fieldsMasks';

import { GridPaginadaRetorno } from 'components/Grid/Paginacao';
import { EditarMassaIcon, PesquisarEstoqueIcon } from 'icons';
import { SearchInput } from 'components/update/Input/SearchInput';
import { ModalConfirmacaoExcluir } from 'components/Modal/ModalConfirmacaoExcluir';
import { ModalConfirmacaoInativar } from 'components/Modal/ModalConfirmacaoInativar';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import ModalListarSaldoVariacoes from 'components/Modal/ModalListarSaldoVariacoes';
import { BuscaAvancadaButton } from 'components/update/BuscaAvancadaButton';
import {
  PagedTable,
  PagedTableForwardRefData,
} from 'components/update/Table/PagedTable';
import { StatusCircle } from 'components/update/Table/StatusCircle';
import { PaginationData } from 'components/update/Pagination';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import { ButtonCadastrarNovo } from 'components/Layout/ButtonCadastrarNovo';

import {
  useForm,
  yupResolver,
  defaultValues,
  FiltrosProps,
  CamposPersonalizadosProps,
  Produto,
} from './validationForm';
import { DrawerBuscaAvancada } from './DrawerBuscaAvancada';
import { validarListaServicos } from 'helpers/validation/validarListaServicos';

export const ListarCadastroProduto = () => {
  const { casasDecimais } = usePadronizacaoContext();
  const history = useHistory();
  const isMountedRef = useIsMountedRef();

  const dadosEmCache = useFiltrosProdutoStore();
  const { setFiltros } = dadosEmCache;

  const [filtrosAtivos, setFiltrosAtivos] = useState(false);
  const [currentFilters, setCurrentFilters] = useState<FiltrosProps>(
    dadosEmCache?.filtros
  );
  const [idProduto, setIdProduto] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [produtos, setProdutos] = useState([] as Array<Produto>);
  const [totalRegistros, setTotalRegistros] = useState(0);
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const [openDrawerBuscaAvancada, setOpenDrawerBuscaAvancada] = useState(false);

  const pageIsLoaded = useRef(false);
  const childRef = useRef<PagedTableForwardRefData>(null);

  const permissaoProdutoAlterar = useMemo(
    () => auth.possuiPermissao(ConstanteFuncionalidades.PRODUTO_ALTERAR),
    []
  );
  const planoContratado = useMemo(() => auth.getPlano(), []);
  const planosNaoPossuemLocalEstoque = [
    PlanoContratacaoEnum.BASIC,
    PlanoContratacaoEnum.START,
    PlanoContratacaoEnum.PRO,
  ];

  const planoNaoPossuiLocalEstoque =
    planosNaoPossuemLocalEstoque.includes(planoContratado);

  const permissaoProdutoVisualizar = auth.possuiPermissao(
    ConstanteFuncionalidades.PRODUTO_VISUALIZAR
  );

  const possuiPermissaoVisualizarPrecoCusto = auth.possuiPermissao(
    ConstanteFuncionalidades.USUARIO_VISUALIZAR_PRECO_CUSTO
  ).permitido;

  const possuiServicoFrenteCaixa = validarListaServicos([
    enumReferenciaServicoStargate.DISPOSITIVO_FRENTE_CAIXA,
    enumReferenciaServicoStargate.MODULO_FRENTE_CAIXA,
  ]);

  const possuiServicoTray = auth.possuiServico(
    enumReferenciaServicoStargate.INTEGRACAO_TRAY
  ).permitido;

  const possuiServicoPdvOffline = auth.possuiServico(
    enumReferenciaServicoStargate.DISPOSITIVO_PDV
  ).permitido;

  const produtoPodeSerRemovido = !(
    possuiServicoFrenteCaixa ||
    possuiServicoTray ||
    possuiServicoPdvOffline
  );

  const formMethods = useForm<FiltrosProps>({
    resolver: yupResolver,
    shouldUnregister: false,
    defaultValues: dadosEmCache?.filtros,
  });

  const handleHistoryPush = useCallback(
    (path: string) => {
      history.push(path);
    },
    [history]
  );

  const filtersSubmit = useCallback((filtros: FiltrosProps) => {
    setCurrentFilters(filtros);
    setFiltros({
      filtros,
    });
  }, []);

  const getNomeLinkHref = useCallback(
    (id: string) => {
      let href = '';

      if (permissaoProdutoAlterar.permitido) {
        href = SubstituirParametroRota(
          ConstanteRotas.PRODUTO_ALTERAR,
          'id',
          id
        );
      } else if (permissaoProdutoVisualizar.permitido) {
        href = SubstituirParametroRota(
          ConstanteRotas.PRODUTO_VISUALIZAR,
          'id',
          id
        );
      }

      return href;
    },
    [permissaoProdutoAlterar, permissaoProdutoVisualizar]
  );

  const handleDuplicar = useCallback(
    async (id: string) => {
      setIsLoading(true);
      const response = await api.post<void, ResponseApi<string>>(
        `${ConstanteEnderecoWebservice.PRODUTOS_CADASTRAR_DADOS_GERAIS_V2}/${id}`
      );

      if (response) {
        if (response.avisos) {
          response.avisos.forEach((item: string) => toast.warning(item));
        }
        if (response.sucesso) {
          const rota = getNomeLinkHref(response.dados);
          history.push(rota, { isDuplicar: true });
        }
        setIsLoading(false);
      }
      setIsLoading(false);
    },
    [getNomeLinkHref, history]
  );

  const handleSetFiltrosAtivos = useCallback(() => {
    const filtros = currentFilters;
    const {
      camposPersonalizados: filtrosCamposPersonalizados,
      ...restOfFiltros
    } = filtros;

    if (
      (filtrosCamposPersonalizados &&
        (
          filtrosCamposPersonalizados?.filter(
            (campoPersonalizado) =>
              campoPersonalizado &&
              campoPersonalizado !== null &&
              campoPersonalizado.campoPersonalizadoId &&
              campoPersonalizado.campoPersonalizadoId !== null &&
              campoPersonalizado.valor !== undefined &&
              campoPersonalizado.valor !== 'undefined' &&
              campoPersonalizado.valor !== null &&
              campoPersonalizado.valor !== 'null' &&
              String(campoPersonalizado.valor)
          ) || []
        ).length > 0) ||
      JSON.stringify(restOfFiltros) !== JSON.stringify(defaultValues)
    ) {
      setFiltrosAtivos(true);
    } else {
      setFiltrosAtivos(false);
    }
  }, [currentFilters]);

  const isValidCampoPersonalizado = (
    item: unknown
  ): item is CamposPersonalizadosProps => {
    if (typeof item !== 'object' || item === null) {
      return false;
    }

    const obj = item as Partial<CamposPersonalizadosProps>;

    const idValido =
      typeof obj.campoPersonalizadoId === 'string' &&
      obj.campoPersonalizadoId.trim() !== '';

    const valorValido =
      (typeof obj.valor === 'string' && obj.valor.trim() !== '') ||
      typeof obj.valor === 'number' ||
      (typeof obj.valor === 'boolean' && obj.valor);

    return idValido && valorValido;
  };

  const paginationHandle = useCallback(
    async (gridPaginadaConsulta: PaginationData) => {
      setIsLoading(true);

      const {
        cores,
        categoriasProduto,
        marcas,
        tamanhos,
        camposPersonalizados,
        tags,
      } = currentFilters;

      const camposPersonalizadosComValor = camposPersonalizados
        ? (camposPersonalizados || [])
            .filter(isValidCampoPersonalizado)
            .map(({ campoPersonalizadoId, valor }) => ({
              campoPersonalizadoId,
              valor: String(valor),
            }))
        : [];

      const filtros = {
        ...currentFilters,
        cores: (cores || []).length > 0 && cores ? cores : null,
        categoriasProduto:
          (categoriasProduto || []).length > 0 && categoriasProduto
            ? categoriasProduto
            : null,
        marcas: (marcas || []).length > 0 && marcas ? marcas : null,
        tags: (tags || []).length > 0 && tags ? tags : null,
        tamanhos: (tamanhos || []).length > 0 && tamanhos ? tamanhos : null,
        camposPersonalizados: camposPersonalizadosComValor,
      };

      const response = await api.post<
        void,
        ResponseApi<GridPaginadaRetorno<Produto>>
      >(
        formatQueryPagegTable(
          ConstanteEnderecoWebservice.PRODUTO_LISTAR_PAGINADO,
          gridPaginadaConsulta
        ),
        {
          ...filtros,
        }
      );

      if (response) {
        if (response.sucesso && isMountedRef.current) {
          setTotalRegistros(response.dados.total);
          setProdutos(response.dados.registros);

          formMethods.reset(filtros, {
            keepIsSubmitted: true,
            keepDirty: false,
          });
          setFiltros({
            filtros,
            paginacao: gridPaginadaConsulta,
          });
          handleSetFiltrosAtivos();
        }
      }

      if (isMountedRef.current) {
        setIsLoading(false);

        if (!pageIsLoaded.current) {
          pageIsLoaded.current = true;

          formMethods.setFocus('nomeReferencia');
        }
      }
    },
    [
      currentFilters,
      formMethods,
      handleSetFiltrosAtivos,
      isMountedRef,
      setFiltros,
    ]
  );

  const handleRefresh = useCallback(() => {
    return childRef.current?.reload();
  }, []);

  const handleReset = formMethods.handleSubmit((data) => {
    const filtersIsDirty = !shallowEqual(data, currentFilters || {});

    if (filtersIsDirty) {
      setCurrentFilters(data);
      setFiltros({
        filtros: data,
      });
    }
  });

  const inativarProduto = useCallback(
    (response: ResponseApi | null, produtoId: string, ativo: boolean) => {
      ModalConfirmacaoInativar({
        response,
        rotaWebService: ConstanteEnderecoWebservice.PRODUTO_INATIVAR,
        id: produtoId,
        ativo,
        callback: (okInativar: boolean) => {
          if (okInativar) handleRefresh();
        },
      });
    },
    [handleRefresh]
  );

  const excluirProduto = useCallback(
    async (produtoId: string, ativo: boolean) => {
      ModalConfirmacaoExcluir({
        callback: async (ok: boolean) => {
          if (ok) {
            setIsLoading(true);

            const response = await api.delete<void, ResponseApi>(
              ConstanteEnderecoWebservice.PRODUTO_EXCLUIR,
              {
                params: { id: produtoId },
              }
            );

            if (response?.sucesso) {
              toast.success('O cadastro foi removido com sucesso.');

              handleRefresh();
            }

            if (response) {
              inativarProduto(response, produtoId, ativo);
            }

            setIsLoading(false);
          }
        },
      });
    },
    [handleRefresh, inativarProduto]
  );

  useEffect(() => {
    handleSetFiltrosAtivos();
  }, [handleSetFiltrosAtivos]);

  return (
    <FormProvider {...formMethods}>
      <Flex
        flexDirection={{ base: 'column', md: 'row' }}
        width="full "
        align="center"
        justify="space-between"
        gap={{ base: '8px', md: '24px' }}
      >
        <Stack
          direction={{ base: 'column', md: 'row' }}
          width="full"
          gap={{ base: '8px', md: '24px' }}
          align="center"
        >
          <Box width="full" maxWidth={{ base: 'full', md: '560px' }}>
            <SearchInput
              type="search"
              id="nomeReferencia"
              name="nomeReferencia"
              placeholder="Buscar o produto por nome, cód. SKU, GTIN/EAN, cód. externo e cód. de barras interno"
              onEnterKeyPress={() => handleReset()}
              isDisabled={isLoading}
            />
          </Box>
          <BuscaAvancadaButton
            minWidth={{ base: 'full', md: '168px' }}
            maxWidth={{ base: 'full', md: '168px' }}
            bg="none"
            variant="outlineDefault"
            colorScheme="gray.500"
            borderRadius="full"
            fontSize="14px"
            letterSpacing={0}
            justifyContent="center"
            hasFilters={filtrosAtivos}
            isDisabled={isLoading}
            setIsModalBuscaAvancadaOpen={() => setOpenDrawerBuscaAvancada(true)}
          />
          <Box width={{ base: 'full', md: '174px' }}>
            <ButtonCadastrarNovo
              minWidth={{ base: 'full', md: '174px' }}
              maxWidth={{ base: 'full', md: '174px' }}
              bg="none"
              variant="outlineDefault"
              label="Edição em massa"
              colorScheme="gray"
              borderRadius="full"
              fontSize="14px"
              borderColor="gray.200"
              leftIcon={<Icon as={EditarMassaIcon} mr={0} boxSize="18px" />}
              funcionalidade={ConstanteFuncionalidades.PROUTO_ALTERAR_EM_MASSA}
              isDisabled={isLoading}
              height="36px"
              onClick={() =>
                history.push(ConstanteRotas.PRODUTO_EDICAO_EM_MASSA)
              }
            />
          </Box>
        </Stack>
        <Box width={{ base: 'full', md: '168px' }}>
          <ButtonCadastrarNovo
            maxWidth={{ base: 'full', md: '168px' }}
            variant="solid"
            color="primary.700"
            colorScheme="secondary.400"
            onClick={() => handleHistoryPush(ConstanteRotas.PRODUTO_CADASTRAR)}
            funcionalidade={ConstanteFuncionalidades.PRODUTO_CADASTRAR}
          />
        </Box>
      </Flex>
      <SimpleGridForm
        gap={{ base: '10px', sm: '10px', md: 8 }}
        mt={{ base: 3, sm: 6, md: 8 }}
      >
        <ModalListarSaldoVariacoes
          isOpen={modalIsOpen}
          setIsOpen={setModalIsOpen}
          idProduto={idProduto}
        />
        <GridItem mt={['10px', '10px', '-10px']} colSpan={12}>
          <PagedTable
            loadColumnsData={paginationHandle}
            itemsTotalCount={totalRegistros}
            cachePaginacao={dadosEmCache?.paginacao}
            defaultKeyOrdered="Nome"
            isLoading={isLoading}
            tableHeaders={[
              {
                content: <StatusCircle hasValue={false} />,
                key: 'Ativo',
                isOrderable: true,
                width: '37px',
              },
              {
                content: 'Nome',
                key: 'Nome',
                isOrderable: true,
                width: 'auto',
              },
              {
                content: 'Referência',
                key: 'Referencia',
                isOrderable: false,
                width: 'auto',
              },
              possuiPermissaoVisualizarPrecoCusto
                ? {
                    content: (
                      <Flex alignItems="center" justifyContent="flex-end">
                        Preço de custo
                      </Flex>
                    ),
                    key: 'PrecoCusto',
                    isOrderable: false,
                    width: '150px',
                  }
                : {
                    content: <></>,
                    key: '',
                  },
              {
                content: (
                  <Flex alignItems="center" justifyContent="flex-end">
                    Preço de venda
                  </Flex>
                ),
                key: 'PrecoVenda',
                isOrderable: false,
                width: '150px',
              },
              {
                content: (
                  <Flex alignItems="center" justifyContent="flex-end">
                    Estoque
                  </Flex>
                ),
                key: 'Estoque',
                isOrderable: false,
                width: '150px',
              },
              {
                content: '',
                key: '',
                isOrderable: false,
                width: '80px',
              },
              {
                content: 'Ações',
                key: 'Acoes',
                isOrderable: false,
                width: '38px',
              },
            ]}
            ref={childRef}
            renderTableRows={produtos.map((produto) => {
              const hasEstoque = produto.estoque > 0;

              return (
                <Tr
                  sx={{
                    '& td': {
                      color: hasEstoque ? 'gray.700' : 'red.500',
                    },
                  }}
                  key={produto.id}
                >
                  <Td style={{ textAlign: 'center' }}>
                    <StatusCircle isActive={produto.ativo} />
                  </Td>
                  <Td>
                    <Link
                      id="link-visualizar"
                      href={getNomeLinkHref(produto.id)}
                    >
                      {produto.nome}
                    </Link>
                  </Td>
                  <Td>{produto.referencia || '---'}</Td>
                  {possuiPermissaoVisualizarPrecoCusto ? (
                    <Td>
                      <Flex justifyContent="flex-end">
                        {moneyMask(produto.precoCusto || 0, true)}
                      </Flex>
                    </Td>
                  ) : (
                    <Td />
                  )}
                  <Td>
                    <Flex justifyContent="flex-end">
                      {moneyMask(produto.precoVenda, true)}
                    </Flex>
                  </Td>
                  <Td
                    textAlign="end"
                    color={produto.estoque <= 0 ? 'red.400' : undefined}
                  >
                    <Flex alignItems="center" justifyContent="flex-end">
                      <Text>
                        {produto.estoque.toLocaleString('locale', {
                          minimumFractionDigits:
                            casasDecimais.casasDecimaisQuantidade,
                          maximumFractionDigits:
                            casasDecimais.casasDecimaisQuantidade,
                        })}
                      </Text>
                    </Flex>
                  </Td>
                  {planoNaoPossuiLocalEstoque ? (
                    <Td />
                  ) : (
                    <Td pt="0" pb="0">
                      <Button
                        pr="0"
                        fontSize="lg"
                        cursor={
                          planoNaoPossuiLocalEstoque ? 'not-allowed' : 'pointer'
                        }
                        variant="none"
                        _focus={{ border: 'none' }}
                        style={{ position: 'relative', top: '-3px' }}
                        onClick={() => {
                          setModalIsOpen(true);
                          setIdProduto(produto.id);
                        }}
                        isDisabled={planoNaoPossuiLocalEstoque}
                      >
                        <Icon
                          color={
                            produto.estoque <= 0 ? 'red.400' : 'primary.50'
                          }
                          cursor={
                            planoNaoPossuiLocalEstoque
                              ? 'not-allowed'
                              : 'pointer'
                          }
                          as={PesquisarEstoqueIcon}
                          fontSize="lg"
                        />
                      </Button>
                    </Td>
                  )}

                  <Td isNumeric>
                    <Flex alignItems="center" justifyContent="flex-end">
                      <ActionsMenu
                        id="mostrarMais"
                        items={[
                          {
                            content: 'Editar',
                            onClick: () => {
                              handleHistoryPush(
                                SubstituirParametroRota(
                                  ConstanteRotas.PRODUTO_ALTERAR,
                                  'id',
                                  produto.id
                                )
                              );
                            },
                            funcionalidade:
                              ConstanteFuncionalidades.PRODUTO_ALTERAR,
                          },
                          produtoPodeSerRemovido
                            ? {
                                content: 'Remover',
                                onClick: () => {
                                  excluirProduto(produto.id, produto.ativo);
                                },
                                funcionalidade:
                                  ConstanteFuncionalidades.PRODUTO_EXCLUIR,
                              }
                            : {
                                content: 'Inativar',
                                onClick: () => {
                                  inativarProduto(
                                    null,
                                    produto.id,
                                    produto.ativo
                                  );
                                },
                                funcionalidade:
                                  ConstanteFuncionalidades.PRODUTO_EXCLUIR,
                              },
                          {
                            content: 'Duplicar',
                            onClick: () => {
                              handleDuplicar(produto.id);
                            },
                            funcionalidade:
                              ConstanteFuncionalidades.PRODUTO_CADASTRAR,
                          },
                        ]}
                      />
                    </Flex>
                  </Td>
                </Tr>
              );
            })}
          />
        </GridItem>
        <DrawerBuscaAvancada
          currentFilters={currentFilters}
          filtersSubmit={filtersSubmit}
          isOpen={openDrawerBuscaAvancada}
          onClose={() => setOpenDrawerBuscaAvancada(false)}
          filtrosReset={defaultValues}
        />
      </SimpleGridForm>
    </FormProvider>
  );
};
