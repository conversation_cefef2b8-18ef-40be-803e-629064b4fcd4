import {
  Box,
  Button,
  GridItem,
  Icon,
  Text,
  Flex,
  Tooltip,
} from '@chakra-ui/react';
import { MdDragHandle } from 'react-icons/md';

import { getName } from 'helpers/enum/getName';
import enumTipoItemProdutoEtapa from 'constants/enum/enumTipoItemProdutoEtapa';

import { InfoIconCircular, SalvarInserirNovoIcon } from 'icons';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';
import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';

import { FILTRO_TODAS_ETAPAS, useProdutoEtapa } from './hooks';
import { FiltroTelaExibicao } from './components/FiltroTelaExibicao';

export const FormacaoEtapaProduto = () => {
  const {
    handleRegisterStep,
    handleUpdateStep,
    handleDeleteStep,
    handleDragOver,
    handleDragStart,
    handleDrag,
    handleDragEnd,
    handleTouchStart,
    handleTouchMove,
    dragOverItemIndex,
    isDragging,
    isLoading,
    listaEtapasFiltrada,
    readonly,
    obterOndeSeraExibidoEtapa,
    exibirFiltro,
    opcoesFiltro,
    aplicarFiltro,
    opcoesLocaisExibicao,
    valorFiltroAtivo,
    podeArrastarEtapas,
  } = useProdutoEtapa();

  return (
    <SimpleGridForm>
      <GridItem colSpan={12}>
        <Button
          type="button"
          variant="solid"
          colorScheme="blue"
          leftIcon={<Icon as={SalvarInserirNovoIcon} />}
          marginBottom="4px"
          maxWidth="200px"
          maxHeight="32px"
          w="100%"
          fontStyle="italic"
          fontSize="14px"
          onClick={handleRegisterStep}
          fontWeight="normal"
        >
          Adicionar nova etapa
        </Button>

        {isLoading && <LoadingPadrao />}
        {listaEtapasFiltrada.length > 0 ? (
          <Box
            p={6}
            bg="gray.200"
            borderWidth="1px"
            borderColor="gray.200"
            borderRadius="5px"
            position="relative"
            overflowX={['scroll', 'scroll', 'scroll', 'hidden']}
            overflowY="hidden"
            onDrop={(e) => {
              e.preventDefault();
            }}
          >
            <Box
              overflowX={['scroll', 'scroll', 'scroll', 'hidden']}
              pb="10px"
              px={1}
            >
              <Flex
                w={['812px', '884px', '1200px', 'full']}
                pr={3}
                pl={exibirFiltro ? 0 : 3}
                gap={3}
                alignItems="flex-end"
              >
                {exibirFiltro ? (
                  <Flex w="calc(45% + 32px)">
                    <Box whiteSpace="nowrap" textColor="black" fontSize="10px">
                      <Flex mb="4px" align="center" gap="6px" w="340px">
                        <FiltroTelaExibicao
                          items={opcoesFiltro()}
                          aplicarFiltro={aplicarFiltro}
                        />
                        {valorFiltroAtivo !== FILTRO_TODAS_ETAPAS && (
                          <Tooltip
                            hasArrow
                            bg="black"
                            placement="bottom-start"
                            label="Não é possível editar a ordenação das etapas quando um filtro está aplicado. Para alterar a ordem de exibição selecione a opção “Todas as etapas”."
                            p="20px"
                            borderRadius="6px"
                            tabIndex={-1}
                            maxW="344px"
                          >
                            <Box tabIndex={-1} as="span" ml="4px">
                              <Icon as={InfoIconCircular} w="12px" h="12px" />
                            </Box>
                          </Tooltip>
                        )}
                      </Flex>
                    </Box>
                  </Flex>
                ) : (
                  <Flex w="calc(45% + 24px)">
                    <Box>
                      <MdDragHandle size="20px" color="transparent" />
                    </Box>
                    <Box
                      whiteSpace="nowrap"
                      textColor="black"
                      fontSize="10px"
                      pl="20px"
                    >
                      Texto de exibição
                    </Box>
                  </Flex>
                )}
                <Box
                  w="10%"
                  whiteSpace="nowrap"
                  textColor="black"
                  fontSize="10px"
                >
                  Tipo
                </Box>
                <Box
                  w="20%"
                  whiteSpace="nowrap"
                  textColor="black"
                  fontSize="10px"
                >
                  Onde exibir
                </Box>
                <Box
                  w="20%"
                  whiteSpace="nowrap"
                  textColor="black"
                  fontSize="10px"
                >
                  Qtde. Máx. Escolhas
                </Box>
                <Box
                  w="5%"
                  textAlign="end"
                  mr="18px"
                  whiteSpace="nowrap"
                  textColor="black"
                  fontSize="10px"
                >
                  Ações
                </Box>
              </Flex>
              <Flex
                w={['812px', '884px', '1200px', 'full']}
                flexDir="column"
                gap="5px"
                pointerEvents={readonly ? 'none' : 'unset'}
              >
                {listaEtapasFiltrada?.map((step, index) => {
                  const isDraggingOverItem =
                    isDragging && dragOverItemIndex === index;
                  const ordem = index + 1;
                  return (
                    <Flex
                      key={step.id}
                      draggable={podeArrastarEtapas}
                      h="50px"
                      w="full"
                      px={3}
                      gap={3}
                      align="center"
                      cursor="grab"
                      borderWidth={2}
                      transitionProperty="background-color, border-color"
                      transitionDuration=".2s"
                      transitionTimingFunction="linear"
                      borderRadius="md"
                      borderStyle="dashed"
                      userSelect="none"
                      backgroundColor={
                        isDraggingOverItem
                          ? '#ECE9FF !important'
                          : podeArrastarEtapas
                          ? 'white'
                          : '#FFFFFF50'
                      }
                      borderColor={
                        isDraggingOverItem ? 'purple.500' : 'transparent'
                      }
                      boxShadow={
                        isDraggingOverItem
                          ? 'none !important'
                          : '0px 0px 4px #00000029'
                      }
                      sx={{
                        '& > div': {
                          opacity: isDraggingOverItem ? 0.5 : 1,
                        },
                      }}
                      _hover={{
                        boxShadow: isDragging
                          ? '0px 0px 4px #00000029 !important'
                          : '0px 0px 4px rgba(85, 2, 178, 0.5)',
                      }}
                      onDragStart={handleDragStart}
                      onDrag={handleDrag}
                      onDragEnter={(event) => handleDragOver(event, index)}
                      onDragEnd={(event) => handleDragEnd(event, index)}
                      onTouchStart={(e) => {
                        if (podeArrastarEtapas) handleTouchStart(e);
                      }}
                      onTouchMove={(event) => {
                        if (podeArrastarEtapas) handleTouchMove(event, index);
                      }}
                      onTouchEnd={(event) => {
                        if (podeArrastarEtapas) handleDragEnd(event, index);
                      }}
                    >
                      <Flex w="calc(45% + 6px)" gap="24px">
                        <Box textAlign="center">
                          {ordem < 10 ? `0${ordem}` : ordem}
                        </Box>
                        <Box>
                          <Text
                            width="min"
                            cursor="pointer"
                            whiteSpace="nowrap"
                            onClick={() => handleUpdateStep(step.id)}
                            _hover={{ textDecor: 'underline' }}
                          >
                            {step.textoParaExibicao || '-----------'}
                          </Text>
                        </Box>
                      </Flex>
                      <Box w="10%">
                        {getName(enumTipoItemProdutoEtapa, step.tipoEtapa)}
                      </Box>
                      <Box w="20%">
                        {exibirFiltro &&
                        valorFiltroAtivo !== FILTRO_TODAS_ETAPAS
                          ? opcoesLocaisExibicao[valorFiltroAtivo]
                          : obterOndeSeraExibidoEtapa(step.telasExibicao)}
                      </Box>
                      <Box w="20%" pl="36px">
                        {step.quantidadeMaxima}
                      </Box>
                      <Flex w="5%" justify="flex-end">
                        <ActionsMenu
                          isDisabled={readonly}
                          items={[
                            {
                              content: 'Editar',
                              onClick: () => handleUpdateStep(step.id),
                            },
                            {
                              content: 'Remover',
                              onClick: () => handleDeleteStep(step.id),
                            },
                          ]}
                        />
                      </Flex>
                    </Flex>
                  );
                })}
              </Flex>
            </Box>
          </Box>
        ) : (
          <Box
            bg="white"
            minHeight="64px"
            p={['12px', '22px 40px']}
            borderRadius="5px"
            borderColor="gray.200"
            borderWidth="1px"
          >
            Não existem etapas cadastradas para este produto.
          </Box>
        )}
      </GridItem>
    </SimpleGridForm>
  );
};
