import { useMediaQuery } from '@chakra-ui/react';
import { useCallback, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useFormContext } from 'react-hook-form';

import api, { ResponseApi } from 'services/api';
import ConstanteRotas from 'constants/rotas';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import useReloadRegistration from 'helpers/layout/useReloadRegistration';
import { usePromocaoContext } from 'store/Promocao';

import { FormData } from '../validationForms';
import { useFormularioPromocao } from '../hooks';

export const usePromocaoCadastrar = () => {
  const history = useHistory();

  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const { isLoading, setIsLoading, formIsDirty, setFormIsDirty } =
    usePromocaoContext();

  const {
    handleSubmit,
    formState: { isDirty },
    getValues,
  } = useFormContext<FormData>();

  const historyReload = useReloadRegistration({ setFormIsDirty });

  const { configuracaoPromocao, cadastrarProdutos, possuiServicoTray } =
    useFormularioPromocao();

  const cadastrarPromocao = useCallback(async () => {
    const data = await configuracaoPromocao();

    const valuesForm = getValues();
    setFormIsDirty(false);

    if (data === false) {
      return false;
    }

    const response = await api.post<void, ResponseApi<string>>(
      ConstanteEnderecoWebservice.PROMOCAO,
      {
        ...data,
      }
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
        return false;
      }

      if (response?.sucesso && response?.dados) {
        if ((valuesForm.produtos || []).length > 0) {
          const listProdutos = valuesForm.produtos.map((itemProduto) => ({
            precoPromocao: itemProduto.precoNovo,
            produtoCorTamanhoId: itemProduto.produtoCorTamanhoId || '',
            quantidade: itemProduto.quantidade || 0,
            descontoPorQuantidade: itemProduto.descontoPorQuantidade,
            criterioPromocaoQuantidade: itemProduto.criterioPromocaoQuantidade,
          }));

          const sucessProdutosCadastrados = await cadastrarProdutos(
            response.dados,
            listProdutos
          );

          return sucessProdutosCadastrados;
        }

        return true;
      }
    }
    setIsLoading(false);
    return false;
  }, [
    cadastrarProdutos,
    configuracaoPromocao,
    getValues,
    setFormIsDirty,
    setIsLoading,
  ]);

  const onSubmit = handleSubmit(async () => {
    setIsLoading(true);

    const response = await cadastrarPromocao();

    if (response) {
      toast.success('Cadastro salvo com sucesso');
      history.push(ConstanteRotas.PROMOCAO);
    }

    setIsLoading(false);
  });

  const onSubmitReset = handleSubmit(async () => {
    setIsLoading(true);

    const response = await cadastrarPromocao();

    if (response) {
      toast.success('Cadastro salvo com sucesso');
      historyReload(ConstanteRotas.PROMOCAO_CADASTRAR);
    }

    setIsLoading(false);
  });

  useEffect(() => {
    setFormIsDirty(isDirty);
  }, [isDirty, setFormIsDirty]);

  return {
    isLoading,
    onSubmit,
    onSubmitReset,
    formIsDirty,
    isLargerThan700,
    possuiServicoTray,
  };
};
