import { useMemo } from 'react';
import { Flex, Text, Button, Icon, Tr, Td } from '@chakra-ui/react';
import { IoIosCheckmarkCircleOutline } from 'react-icons/io';

import auth from 'modules/auth';
import PlanoContratacaoEnum from 'constants/enum/planoContratacao';
import TipoProdutoEnum from 'constants/enum/tipoProduto';
import { moneyMask } from 'helpers/format/fieldsMasks';
import {
  Produto,
  useConsultaProdutosPdvContext,
} from 'store/PDV/ConsultaProdutoPdv';

import { PagedTable } from 'components/update/Table/PagedTable';
import { PesquisarEstoqueIcon } from 'icons';

import { EnumTelaConsultaProdutosPdv } from '..';

interface ConsultaProdutoProps {
  tela: EnumTelaConsultaProdutosPdv;
  casasDecimais: { casasDecimaisQuantidade: number };
  produtoSelecionado: Produto | null;
  exibirBotaoAdicionarProduto: boolean;
  isOpenDrawer: boolean;
  toggleSelect: (produto: Produto) => void;
  lancarProdutoSimples: (produto: Produto) => void;
  setProdutoSelecionado: (produto: Produto | null) => void;
  setModalVisualizarEstoqueEstaAberto: (aberto: boolean) => void;
  escolherVariacao: (id: string) => void;
  onCloseDrawer: () => void;
}

export const ConsultaProdutos = ({
  tela,
  casasDecimais,
  produtoSelecionado,
  exibirBotaoAdicionarProduto,
  isOpenDrawer,
  setProdutoSelecionado,
  setModalVisualizarEstoqueEstaAberto,
  escolherVariacao,
  toggleSelect,
  lancarProdutoSimples,
  onCloseDrawer,
}: ConsultaProdutoProps) => {
  const { paginationHandle, produtos, totalRegistros } =
    useConsultaProdutosPdvContext();
  if (tela !== EnumTelaConsultaProdutosPdv.PRODUTOS) {
    return null;
  }

  const cabecalhoHeader = () => {
    return [
      {
        content: 'Nome',
        key: 'Nome',
        isOrderable: true,
        width: 'auto',
        pl: totalRegistros > 0 ? '72px !important' : '40px !important',
      },
      {
        content: (
          <Flex alignItems="center" justifyContent="flex-end">
            Preço de venda
          </Flex>
        ),
        key: 'PrecoVenda',
        isOrderable: false,
        width: '150px',
      },
      {
        content: (
          <Flex alignItems="center" justifyContent="flex-end">
            Estoque
          </Flex>
        ),
        key: 'Estoque',
        isOrderable: false,
        width: ['120px', '120px', '200px'],
      },
      {
        content: '',
        key: '',
        isOrderable: false,
        width: '80px',
      },
    ];
  };

  const planoContratado = useMemo(() => auth.getPlano(), []);

  const planosNaoPossuemLocalEstoque = [
    PlanoContratacaoEnum.BASIC,
    PlanoContratacaoEnum.START,
    PlanoContratacaoEnum.PRO,
  ];

  const planoNaoPossuiLocalEstoque =
    planosNaoPossuemLocalEstoque.includes(planoContratado);

  return (
    <>
      <PagedTable
        loadColumnsData={paginationHandle}
        itemsTotalCount={totalRegistros}
        defaultKeyOrdered="Nome"
        itensPerPage={25}
        overflowY="auto"
        maxH="calc(100vh - 150px)"
        tableHeaders={cabecalhoHeader()}
        renderTableRows={produtos?.map((produto) => {
          const hasEstoque = produto.estoque > 0;

          return (
            <Tr
              key={produto.id}
              sx={{
                '& td': {
                  color: hasEstoque ? 'gray.700' : 'red.500',
                },
              }}
              height="52px"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                toggleSelect(produto);
              }}
              onDoubleClick={() => {
                if (produto) {
                  if (
                    produtoSelecionado?.tipoProduto ===
                    TipoProdutoEnum.PRODUTO_VARIACAO
                  ) {
                    setProdutoSelecionado(produto);
                    escolherVariacao(produto.id);
                    return;
                  }
                  if (exibirBotaoAdicionarProduto)
                    lancarProdutoSimples(produto);
                }
              }}
              cursor="pointer"
              bg={produtoSelecionado?.id === produto.id ? 'purple.50' : ''}
            >
              <Td>
                <Text
                  display="flex"
                  alignItems="center"
                  gap="12px"
                  justifyContent="flex-start"
                  pl={produtoSelecionado?.id === produto.id ? '0px' : '32px'}
                >
                  {produtoSelecionado?.id === produto.id && (
                    <IoIosCheckmarkCircleOutline
                      color="#482ABC"
                      fontSize="20px"
                    />
                  )}
                  {produto.nome}
                  {produto.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO && (
                    <Button
                      colorScheme="teal"
                      variant="solid"
                      height="16px"
                      fontWeight="500"
                      fontSize="12px"
                      ml="8px"
                      px="8px"
                      py="10px"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setProdutoSelecionado(produto);
                        escolherVariacao(produto.id);
                      }}
                      onDoubleClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setProdutoSelecionado(produto);
                        escolherVariacao(produto.id);
                      }}
                    >
                      Ver variações
                    </Button>
                  )}
                </Text>
              </Td>
              <Td isNumeric>
                <Flex justifyContent="flex-end">
                  {moneyMask(produto.precoVenda, true)}
                </Flex>
              </Td>
              <Td
                textAlign="end"
                color={produto.estoque <= 0 ? 'red.400' : undefined}
              >
                <Flex alignItems="center" justify="flex-end" gap="24px">
                  <Text>
                    {produto.estoque.toLocaleString('locale', {
                      minimumFractionDigits:
                        casasDecimais.casasDecimaisQuantidade,
                      maximumFractionDigits:
                        casasDecimais.casasDecimaisQuantidade,
                    })}
                  </Text>
                </Flex>
              </Td>
              {planoNaoPossuiLocalEstoque ? (
                <Td />
              ) : (
                <Td pt="0" pb="0">
                  <Icon
                    color={produto.estoque <= 0 ? 'red.400' : 'primary.50'}
                    cursor="pointer"
                    as={PesquisarEstoqueIcon}
                    fontSize="lg"
                    onClick={() => {
                      if (isOpenDrawer) onCloseDrawer();
                      setModalVisualizarEstoqueEstaAberto(true);
                      setProdutoSelecionado(produto);
                    }}
                  />
                </Td>
              )}
            </Tr>
          );
        })}
      />
    </>
  );
};
