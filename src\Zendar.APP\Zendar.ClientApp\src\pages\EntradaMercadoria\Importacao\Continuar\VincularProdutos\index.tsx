import {
  Button,
  Box,
  Tr,
  Td,
  Text,
  Icon,
  Stack,
  Divider,
  HStack,
  Flex,
  useToken,
  useMediaQuery,
} from '@chakra-ui/react';
import React, { useCallback, useState, useMemo } from 'react';
import { FiCheckCircle, FiChevronUp } from 'react-icons/fi';
import { toast } from 'react-toastify';
import { CellMeasurer, CellMeasurerCache } from 'react-virtualized';

import { DecimalMask } from 'helpers/format/fieldsMasks';
import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';

import api, { ResponseApi } from 'services/api';

import { useEntradaMercadoriaDadosCadastroContext } from 'store/EntradaMercadoria/EntradaMercadoriaDadosCadastro';
import { useEntradaMercadoriaEtapasContext } from 'store/EntradaMercadoria/EntradaMercadoriaEtapas';
import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import {
  Container,
  Body,
  StepDescriptionAccordion,
} from 'components/update/Steps/StepContent';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import {
  LoadMoreRowsParams,
  VirtualizedInfiniteTable,
  TableHeader,
} from 'components/update/Table/VirtualizedInfiniteTable';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import TipoProdutoEnum, { TipoProduto } from 'constants/enum/tipoProduto';

import TotalizadoresFixos from './components/TotalizadoresFixos';
import { ModalVincularProduto } from './ModalVincularProduto';
import { TextoTooltip } from './TextoTooltip';

enum EntradaMercadoriaStatusVinculoProduto {
  NAO_VINCULADO = 'NAO_VINCULADO',
  PENDENTE_INFORMAR_VARIACOES = 'PENDENTE_INFORMAR_VARIACOES',
  VINCULADO = 'VINCULADO',
}

type InformacoesRodape = {
  totalProdutos: number;
  quantidadeItens: number;
  valorTotalProdutos: number;
  todosProdutosVinculados: boolean;
};

type Produto = {
  isOpen: boolean;
  documentoFiscalItemId: string;
  descricaoProdutoNota: string;
  statusVinculo: EntradaMercadoriaStatusVinculoProduto;
  dadosAdicionais: string | null;
  quantidade: number;
  valorUnitario: number;
  valorTotal: number;
  cfopEntrada: string;
  cfopNota: string;
  ncm: string;
  codigoCest: string;
  produtoVinculado: {
    id: string;
    nome: null | string;
    conversao: number;
    novaQuantidade: number;
    codigoBarrasNota: string | null;
    codigoBarrasCadastro: string | null;
    dadosAdicionais: string | null;
    tipoProduto: number;
    volumeUnitario: boolean;
    referencia: string | null;
    precoCompra: number;
  } | null;
};

type ProdutoPaginadoRetorno = {
  totalProdutos: number;
  totalItens: number;
  valorTotal: number;
  todosProdutosVinculados: boolean;
  registros: Produto[];
};

const cache = new CellMeasurerCache({
  defaultHeight: 65,
  minHeight: 52,
  fixedWidth: true,
});

export function VincularProdutos() {
  const [teal600, aquamarine100] = useToken('colors', [
    'teal.600',
    'aquamarine.100',
  ]);

  const { casasDecimais } = usePadronizacaoContext();
  const { nextStep, previousStep } = useEntradaMercadoriaEtapasContext();
  const {
    entradaMercadoriaId,
    descartarEntradaMercadoria,
    voltarParaListagem,
    temPermissaoExcluir,
    isReadOnly,
    menuIsOpen,
  } = useEntradaMercadoriaDadosCadastroContext();

  const [isLoading, setIsLoading] = useState(false);
  const [produtos, setProdutos] = useState<Produto[]>([]);
  const [informacoesRodape, setInformacoesRodape] = useState<InformacoesRodape>(
    {
      quantidadeItens: 0,
      totalProdutos: 0,
      valorTotalProdutos: 0,
      todosProdutosVinculados: false,
    }
  );

  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');

  const heightTable = () => {
    const itemOpenInTable = produtos?.filter((produto) => produto.isOpen);
    const numberOfItensOpenInList = itemOpenInTable.length;
    const heightOfAllItensOpen = numberOfItensOpenInList * 112;
    const heightOfAllItensClosed =
      (produtos?.length - numberOfItensOpenInList) * 64;
    return heightOfAllItensOpen + heightOfAllItensClosed;
  };

  const todosProdutosVinculados =
    informacoesRodape.todosProdutosVinculados ||
    (produtos.length === informacoesRodape.totalProdutos &&
      produtos.every(
        (produto) =>
          produto.statusVinculo ===
          EntradaMercadoriaStatusVinculoProduto.VINCULADO
      ));

  function handleDescartarEntradaMercadoria() {
    descartarEntradaMercadoria();
  }

  function handleVoltar() {
    previousStep();
  }

  function handleSalvarRascunho() {
    voltarParaListagem();
  }

  function handleAvancar() {
    if (todosProdutosVinculados) {
      nextStep();
    } else {
      toast.warning('É necessário vincular todos os produtos para continuar.');
    }
  }

  function handleToggleLinhaProduto(index: number) {
    const produtosAtualizados = [...produtos];
    const produtoAtualizado = produtosAtualizados[index];

    produtosAtualizados.splice(index, 1, {
      ...produtoAtualizado,
      isOpen: !produtoAtualizado.isOpen,
    });

    setProdutos(produtosAtualizados);
  }

  function encontrarProximoProdutoParaVincular(
    index: number,
    produtosAtualizados: Produto[]
  ): number | null {
    const produto = produtosAtualizados[index];

    if (
      produto &&
      produto.statusVinculo !== EntradaMercadoriaStatusVinculoProduto.VINCULADO
    ) {
      return index;
    } else if (index < produtosAtualizados.length - 1) {
      return encontrarProximoProdutoParaVincular(
        index + 1,
        produtosAtualizados
      );
    }
    return null;
  }

  async function vincularProduto(
    index: number,
    isEdicao = false,
    produtosAtualizados = produtos,
    produtoPendenteVariacoes?: ProdutoOptionProps
  ) {
    const {
      documentoFiscalItemId,
      descricaoProdutoNota,
      quantidade,
      valorUnitario,
      valorTotal,
      cfopNota,
      cfopEntrada,
      codigoCest,
      ncm,
      produtoVinculado,
      dadosAdicionais,
      statusVinculo,
    } = produtosAtualizados[index];

    const obterCodigoBarras = () => {
      const produtoTipoVariacao =
        produtoVinculado?.tipoProduto === TipoProdutoEnum.PRODUTO_VARIACAO;

      if (produtoTipoVariacao) {
        return '';
      }

      if (produtoVinculado?.codigoBarrasCadastro) {
        return produtoVinculado?.codigoBarrasCadastro;
      }

      if (produtoVinculado?.codigoBarrasNota) {
        return produtoVinculado?.codigoBarrasNota;
      }

      return '';
    };

    if (entradaMercadoriaId) {
      try {
        const {
          success,
          quantidade: novaQuantidade,
          valorUnitario: novoValorUnitario,
          cfop: novoCfop,
          codigoBarrasCadastro: novoCodigoBarrasCadastro,
          temProximoProdutoParaVincular,
          produtoVinculado: novoProdutoVinculado,
        } = await ModalVincularProduto({
          produtoPendenteVariacoes,
          casasDecimaisQuantidade: casasDecimais.casasDecimaisQuantidade,
          casasDecimaisValor: casasDecimais.casasDecimaisValor,
          produto: {
            documentoFiscalItemId,
            descricaoProduto: descricaoProdutoNota,
            quantidade,
            valorUnitario,
            valorTotal,
            cfop: cfopEntrada,
            cfopNota,
            codigoGTINEAN: obterCodigoBarras(),
            codigoBarrasNota: produtoVinculado?.codigoBarrasNota,
            ncm,
            codigoCest,
            dadosAdicionais: dadosAdicionais ?? '',
            tipoProduto: (produtoVinculado?.tipoProduto ??
              TipoProdutoEnum.PRODUTO_SIMPLES) as TipoProduto,
          },
          produtoJaVinculado:
            statusVinculo === EntradaMercadoriaStatusVinculoProduto.VINCULADO,
          totalProdutos: informacoesRodape.totalProdutos,
          entradaMercadoriaId,
          isEdicao,
          numeroItem:
            produtosAtualizados?.filter(
              ({ statusVinculo }) =>
                statusVinculo ===
                EntradaMercadoriaStatusVinculoProduto.VINCULADO
            )?.length + 1,
          proximoItem: index + 1,
        });

        if (success) {
          produtosAtualizados = [...produtosAtualizados];

          const produtoParaAtualizar = produtosAtualizados[index];

          produtosAtualizados.splice(index, 1, {
            ...produtoParaAtualizar,
            statusVinculo: EntradaMercadoriaStatusVinculoProduto.VINCULADO,
            valorUnitario: novoValorUnitario,
            cfopEntrada: novoCfop,
            produtoVinculado: {
              ...produtoParaAtualizar.produtoVinculado,
              nome:
                novoProdutoVinculado?.nome ||
                produtoParaAtualizar.produtoVinculado?.nome,
              tipoProduto:
                novoProdutoVinculado?.tipoProduto ||
                produtoParaAtualizar.produtoVinculado?.tipoProduto,
              codigoBarrasCadastro: novoCodigoBarrasCadastro,
              quantidade: novaQuantidade,
              conversao: parseFloat(
                (novaQuantidade / produtoParaAtualizar.quantidade).toFixed(4)
              ),
              novaQuantidade,
            } as any,
          });

          setProdutos(produtosAtualizados);
        }

        if (temProximoProdutoParaVincular) {
          const proximoIndex = encontrarProximoProdutoParaVincular(
            index + 1,
            produtosAtualizados
          );
          if (proximoIndex !== null) {
            await vincularProduto(proximoIndex, false, produtosAtualizados);
          }
        }
        return success;
      } catch (error) {
        return error;
      }
    }

    return false;
  }

  async function handleEditar(index: number) {
    await vincularProduto(index, true);
  }

  async function handleVincularProduto(
    index: number,
    produtoPendenteVariacoes?: ProdutoOptionProps
  ) {
    await vincularProduto(index, false, produtos, produtoPendenteVariacoes);
  }

  const produtosTableHeaders: TableHeader[] = useMemo(
    () => [
      {
        key: 'descricaoProduto',
        content: 'Produto',
        width: '70%',
        minWidth: '70%',
        paddingLeft: '48px !important',
      },
      {
        key: 'quantidade',
        content: 'Quantidade',
        isNumeric: false,
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
      },
      {
        key: 'valorUnitario',
        content: 'Valor unitário',
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
        isNumeric: true,
      },
      {
        key: 'valorTotal',
        content: 'Valor total',
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
        isNumeric: true,
      },
      {
        key: 'acoes',
        width: '180px',
        minWidth: '180px',
        content: 'Ações',
        textAlign: 'end',
        lineHeight: 'none',
        verticalAlign: 'bottom',
      },
    ],
    []
  );

  function getDynamicHeight(index: number, marginSize: number) {
    const produto = produtos[index];

    const isLastItem = index === produtos.length - 1;

    const closedProdutoHeight = 56 + (isLastItem ? 0 : marginSize);
    const openedProdutoHeight = 124 + (isLastItem ? 0 : marginSize); // 56px da linha e 68px da Box na linha aberta

    const produtoHeight = produto?.isOpen
      ? openedProdutoHeight
      : closedProdutoHeight;

    return produtoHeight;
  }

  const loadMoreRows = useCallback(
    async ({
      currentPage,
      pageSize,
      orderColumn,
      orderDirection,
    }: LoadMoreRowsParams) => {
      if (entradaMercadoriaId) {
        setIsLoading(true);

        const paginationData = {
          currentPage,
          pageSize,
          orderColumn,
          orderDirection,
        };

        const response = await api.get<
          void,
          ResponseApi<ProdutoPaginadoRetorno>
        >(
          formatQueryPagegTable(
            ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_LISTAR_ITENS_PAGINADOS_IMPORTACAO_XML,
            paginationData
          ),
          { params: { id: entradaMercadoriaId } }
        );

        if (response) {
          if (response.sucesso) {
            setProdutos((prev) => [
              ...prev,
              ...(response.dados.registros || []).map(
                (registro) =>
                  ({
                    ...registro,
                    isOpen: false,
                  } as Produto)
              ),
            ]);

            setInformacoesRodape({
              quantidadeItens: response.dados.totalItens,
              totalProdutos: response.dados.totalProdutos,
              valorTotalProdutos: response.dados.valorTotal,
              todosProdutosVinculados: response.dados.todosProdutosVinculados,
            });
          }
        }
      }

      setIsLoading(false);
    },
    [entradaMercadoriaId]
  );

  return (
    <>
      {isLoading && <LoadingPadrao />}
      <Container mt="6px">
        <StepDescriptionAccordion
          stepNumber={2}
          title="Lista de produtos"
          description="Todos os produtos contidos na nota fiscal precisam ser vinculados a um produto existente no sistema. Clique em “vincular ao sistema” em cada um dos itens listados abaixo para realizar esta ação. Caso exista um novo produto você poderá cadastrá-lo na própria tela de vinculação."
        />
        <Body>
          <Box
            display="flex"
            flexDirection="column"
            justifyContent="space-between"
            borderRadius="md"
            border="1px"
            bg="gray.50"
            borderColor="gray.200"
            minH="390px"
            py={{ base: 4, sm: 6, md: 6 }}
            pl={{ base: 4, sm: 6, md: 6 }}
            pr={{ base: '6px', sm: '14px', md: '24px' }}
            sx={{
              '& table': { bg: 'gray.50' },
              '& thead > tr > th': {
                bg: 'gray.50',
                border: 'none',
              },
              '& td:first-of-type': {
                paddingLeft: '16px !important',
              },
              '& tbody > tr': {
                borderRadius: 'md',
                boxShadow: '0px 0px 2px #00000029',
                ...(informacoesRodape.totalProdutos > 0
                  ? { border: '1px', borderColor: 'gray.100' }
                  : {
                      '& > td': {
                        position: 'relative',
                        _before: {
                          content: '""',
                          position: 'absolute',
                          h: 'full',
                          w: 'full',
                          top: 0,
                          left: 0,
                          borderLeft: 'none',
                          borderRight: 'none',
                          borderRadius: 'md',
                        },
                      },
                    }),
              },
              '& tbody > tr > td': {
                bg: 'white',
                lineHeight: 'none',
                _before: {
                  border:
                    informacoesRodape.totalProdutos > 0
                      ? 'none !important'
                      : '1px',
                  borderColor: 'gray.100',
                },
              },
            }}
          >
            <VirtualizedInfiniteTable
              variant="simple-card"
              size="sm"
              bg="gray.50"
              boxShadow="none"
              withoutRowsMessage="Nenhum produto adicionado."
              orderColumn="descricaoProduto"
              tableHeaders={produtosTableHeaders}
              itemHeight={54}
              visibleItemsCount={Math.min(10, informacoesRodape.totalProdutos)} // Limita a altura inicial
              dynamicHeight={({ index }) => getDynamicHeight(index, 5)}
              rowCount={informacoesRodape.totalProdutos}
              isRowLoaded={({ index }) => !!produtos[index]}
              loadMoreRows={loadMoreRows}
              pageSize={20} // Aumenta o tamanho da página para melhor performance
              heightTable={Math.min(heightTable(), 600)} // Limita altura máxima
              rowRenderer={({
                index,
                style: { height, ...restStyle },
                key,
                parent,
              }) => {
                const produto = produtos[index];

                if (!produto) {
                  return null;
                }

                const produtoEstaVinculado =
                  produto.statusVinculo ===
                  EntradaMercadoriaStatusVinculoProduto.VINCULADO;

                const produtoNaoEstaVinculado =
                  produto.statusVinculo ===
                  EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

                const statusQuePodemMostrarDetalhes = [
                  EntradaMercadoriaStatusVinculoProduto.VINCULADO,
                  EntradaMercadoriaStatusVinculoProduto.PENDENTE_INFORMAR_VARIACOES,
                ];

                const codigoBarrasCadastroDiferente = !produto.produtoVinculado
                  ?.codigoBarrasCadastro
                  ? false
                  : produto.produtoVinculado?.codigoBarrasNota !==
                    produto.produtoVinculado?.codigoBarrasCadastro;

                const podeMostrarDetalhes =
                  statusQuePodemMostrarDetalhes.includes(
                    produto.statusVinculo
                  ) || !!produto.dadosAdicionais;

                const obterCorBackground = (
                  status: EntradaMercadoriaStatusVinculoProduto
                ) => {
                  const enumStatus = EntradaMercadoriaStatusVinculoProduto;
                  const vinculado = status === enumStatus.VINCULADO;
                  const naoVinculado = status === enumStatus.NAO_VINCULADO;

                  if (vinculado) return `${teal600} !important`;
                  if (naoVinculado) return 'white';
                  return `${aquamarine100} !important`;
                };

                return (
                  <CellMeasurer
                    cache={cache}
                    columnIndex={1}
                    key={key}
                    parent={parent}
                    rowIndex={index}
                  >
                    {({ registerChild, measure }) => (
                      <>
                        <Tr
                          transition="all 0.3s"
                          ref={(e) => {
                            if (e && registerChild) {
                              registerChild(e);
                            }
                          }}
                          style={restStyle}
                          h={`${getDynamicHeight(index, 0)}px !important`}
                          bg={obterCorBackground(produto.statusVinculo)}
                          sx={{
                            '& > td': {
                              bg: obterCorBackground(produto.statusVinculo),
                              color: produtoEstaVinculado ? 'white' : 'inherit',
                              ...(produto.isOpen
                                ? {
                                    marginBottom: '5px',
                                    borderBottomRadius: '0px !important',
                                  }
                                : {}),
                            },
                          }}
                        >
                          <Td
                            width={produtosTableHeaders[0].width}
                            cursor={podeMostrarDetalhes ? 'pointer' : 'default'}
                            userSelect="none"
                            fontSize="14px"
                            onClick={() => {
                              measure();
                              if (podeMostrarDetalhes)
                                handleToggleLinhaProduto(index);
                            }}
                          >
                            <Button
                              tabIndex={0}
                              bg="transparent"
                              p="4px"
                              pb="0px"
                              mr="6px"
                              h="fit-content"
                              borderRadius="6px"
                              _focus={{
                                background: 'gray.100',
                              }}
                              minW="16px"
                              opacity={podeMostrarDetalhes ? '1' : '0'}
                              pointerEvents={
                                podeMostrarDetalhes ? 'all' : 'none'
                              }
                            >
                              <Icon
                                as={FiChevronUp}
                                mb="6px"
                                transform={
                                  produto.isOpen ? '' : 'rotate(180deg)'
                                }
                                role="button"
                                transition="all 0.3s"
                              />
                            </Button>
                            {produto.descricaoProdutoNota}
                            {produto.isOpen && produto.dadosAdicionais && (
                              <>
                                <Flex
                                  position="absolute"
                                  w="97%"
                                  flexDir="row"
                                  align="center"
                                  pl="30px"
                                  mt="4px"
                                  gap="4px"
                                  fontSize="12px"
                                  fontWeight="bold"
                                >
                                  <TextoTooltip
                                    texto={produto.dadosAdicionais}
                                    maxWidth="100%"
                                  />
                                </Flex>
                              </>
                            )}
                          </Td>
                          <Td
                            width={produtosTableHeaders[1].width}
                            minWidth={produtosTableHeaders[1].width}
                            fontSize="14px"
                          >
                            {DecimalMask(
                              produto.quantidade,
                              casasDecimais.casasDecimaisQuantidade
                            )}
                          </Td>
                          <Td
                            width={produtosTableHeaders[2].width}
                            minWidth={produtosTableHeaders[2].width}
                            isNumeric
                            fontSize="14px"
                          >
                            {DecimalMask(
                              produto.valorUnitario,
                              casasDecimais.casasDecimaisValor
                            )}
                          </Td>
                          <Td
                            width={produtosTableHeaders[3].width}
                            minWidth={produtosTableHeaders[3].width}
                            isNumeric
                            fontSize="14px"
                          >
                            {DecimalMask(produto.valorTotal, 2, 2)}
                          </Td>
                          <Td
                            width={produtosTableHeaders[4].width}
                            minWidth={produtosTableHeaders[4].width}
                          >
                            {produtoEstaVinculado ? (
                              <Flex justifyContent="space-between">
                                <HStack spacing="1" color="secondary.300">
                                  <Icon as={FiCheckCircle} boxSize="4" />
                                  <Text fontSize="xs">Vinculado</Text>
                                </HStack>
                                <ActionsMenu
                                  colorScheme="white"
                                  backgroundHoverColor="gray.500"
                                  items={[
                                    {
                                      content: 'Editar',
                                      onClick: () => handleEditar(index),
                                    },
                                  ]}
                                />
                              </Flex>
                            ) : (
                              <Flex alignItems="center" justifyContent="center">
                                <Button
                                  size="xs"
                                  colorScheme="orange"
                                  minW="136px"
                                  onClick={() => {
                                    if (produtoNaoEstaVinculado) {
                                      handleVincularProduto(index);
                                      return;
                                    }

                                    handleVincularProduto(index, {
                                      id: produto.produtoVinculado?.id || '',
                                      nome:
                                        produto.produtoVinculado?.nome || '',
                                      tipoProduto:
                                        produto.produtoVinculado?.tipoProduto ||
                                        2,
                                      volumeUnitario:
                                        produto.produtoVinculado
                                          ?.volumeUnitario || false,
                                      referencia:
                                        produto.produtoVinculado?.referencia ||
                                        '',
                                      precoCompra:
                                        produto.produtoVinculado?.precoCompra ||
                                        0,
                                      coresOptions: [],
                                      tamanhosOptions: [],
                                    });
                                  }}
                                >
                                  {produtoNaoEstaVinculado
                                    ? 'Vincular ao sistema'
                                    : 'Informar variações'}
                                </Button>
                              </Flex>
                            )}
                          </Td>
                          {produto.isOpen && (
                            <Box
                              h="68px"
                              borderBottomRadius="md"
                              bg={obterCorBackground(produto.statusVinculo)}
                              color={
                                produto.statusVinculo ===
                                EntradaMercadoriaStatusVinculoProduto.VINCULADO
                                  ? 'white'
                                  : 'inherit'
                              }
                              px="5"
                              pt={produto.dadosAdicionais ? '32px' : '0px'}
                              transition="all 0.3s"
                            >
                              <Divider />
                              <Flex
                                h="full"
                                px="5"
                                align="start"
                                py="16px"
                                justify="space-between"
                              >
                                <Text
                                  fontWeight="bold"
                                  textColor={
                                    produto.statusVinculo ===
                                    EntradaMercadoriaStatusVinculoProduto.PENDENTE_INFORMAR_VARIACOES
                                      ? 'gray.700'
                                      : 'secondary.300'
                                  }
                                  fontSize="12px"
                                >
                                  <Text as="span" fontWeight="light">
                                    Produto vinculado:{' '}
                                  </Text>
                                  {produto.produtoVinculado?.nome}
                                </Text>
                                <Flex gap={{ base: '32px', lg: '164px' }}>
                                  <HStack gap={{ base: '32px', lg: '164px' }}>
                                    <Box
                                      as="table"
                                      sx={{
                                        bg: obterCorBackground(
                                          produto.statusVinculo
                                        ),
                                        '& tr': {
                                          borderRadius: 'md',
                                          boxShadow: 'none',
                                        },
                                      }}
                                    >
                                      <Box
                                        as="tr"
                                        sx={{
                                          '& td': {
                                            bg: obterCorBackground(
                                              produto.statusVinculo
                                            ),
                                            height: '22px !important',
                                            padding: '0px !important',
                                            paddingLeft: '6px !important',
                                          },
                                          '& td:first-of-type': {
                                            paddingLeft: '0px !important',
                                          },
                                        }}
                                      >
                                        <Box as="td" textAlign="right">
                                          <Text
                                            fontWeight="light"
                                            whiteSpace="nowrap"
                                          >
                                            Conversão:{' '}
                                          </Text>
                                        </Box>
                                        <Box as="td">
                                          <Text
                                            fontWeight="bold"
                                            fontSize="12px"
                                            whiteSpace="nowrap"
                                          >
                                            {
                                              produto.produtoVinculado
                                                ?.conversao
                                            }
                                            x
                                          </Text>
                                        </Box>
                                      </Box>
                                      <Box
                                        as="tr"
                                        sx={{
                                          '& td': {
                                            bg: obterCorBackground(
                                              produto.statusVinculo
                                            ),
                                            height: '22px !important',
                                            padding: '0px !important',
                                            paddingLeft: '6px !important',
                                          },
                                          '& td:first-of-type': {
                                            paddingLeft: '0px !important',
                                          },
                                        }}
                                      >
                                        <Box as="td" textAlign="right">
                                          <Text
                                            fontWeight="light"
                                            whiteSpace="nowrap"
                                          >
                                            Nova Quantidade:{' '}
                                          </Text>
                                        </Box>
                                        <Box as="td">
                                          <Text
                                            fontWeight="bold"
                                            whiteSpace="nowrap"
                                          >
                                            {
                                              produto.produtoVinculado
                                                ?.novaQuantidade
                                            }
                                          </Text>
                                        </Box>
                                      </Box>
                                    </Box>
                                    <Box
                                      as="table"
                                      sx={{
                                        bg: obterCorBackground(
                                          produto.statusVinculo
                                        ),
                                        '& tr': {
                                          borderRadius: 'md',
                                          boxShadow: 'none',
                                        },
                                      }}
                                    >
                                      <Box
                                        as="tr"
                                        sx={{
                                          '& td': {
                                            bg: obterCorBackground(
                                              produto.statusVinculo
                                            ),
                                            height: '22px !important',
                                            padding: '0px !important',
                                            paddingLeft: '6px !important',
                                          },
                                          '& td:first-of-type': {
                                            paddingLeft: '6px !important',
                                          },
                                        }}
                                      >
                                        <Box as="td" textAlign="right">
                                          <Text
                                            fontWeight="light"
                                            whiteSpace="nowrap"
                                          >
                                            Código de barras da nota:{' '}
                                          </Text>
                                        </Box>
                                        <Box as="td">
                                          <Text
                                            fontWeight="bold"
                                            fontSize="12px"
                                            whiteSpace="nowrap"
                                            paddingRight="6px !important"
                                          >
                                            {produto.produtoVinculado
                                              ?.codigoBarrasNota ||
                                              'Não informado'}
                                          </Text>
                                        </Box>
                                      </Box>
                                      <Box
                                        as="tr"
                                        sx={{
                                          '& td': {
                                            bg: obterCorBackground(
                                              produto.statusVinculo
                                            ),
                                            height: '22px !important',
                                            padding: '0px !important',
                                            paddingLeft: '0px !important',
                                            textColor:
                                              codigoBarrasCadastroDiferente
                                                ? 'black'
                                                : 'inherit',
                                          },
                                          '& td:first-of-type': {
                                            paddingLeft: '0px !important',
                                          },
                                        }}
                                      >
                                        <Box as="td" textAlign="right">
                                          <Text
                                            width="min"
                                            height="full"
                                            fontWeight="light"
                                            whiteSpace="nowrap"
                                            paddingLeft="6px !important"
                                            bg={
                                              codigoBarrasCadastroDiferente
                                                ? 'yellow.600'
                                                : obterCorBackground(
                                                    produto.statusVinculo
                                                  )
                                            }
                                          >
                                            Código de barras do cadastro:{' '}
                                          </Text>
                                        </Box>
                                        <Box as="td">
                                          <Text
                                            width="min"
                                            height="full"
                                            lineHeight="full"
                                            fontWeight="bold"
                                            whiteSpace="nowrap"
                                            paddingX="6px !important"
                                            bg={
                                              codigoBarrasCadastroDiferente
                                                ? 'yellow.600'
                                                : obterCorBackground(
                                                    produto.statusVinculo
                                                  )
                                            }
                                          >
                                            {produto.produtoVinculado
                                              ?.codigoBarrasCadastro ||
                                              'Não informado'}
                                          </Text>
                                        </Box>
                                      </Box>
                                    </Box>
                                  </HStack>
                                </Flex>
                              </Flex>
                            </Box>
                          )}
                        </Tr>
                      </>
                    )}
                  </CellMeasurer>
                );
              }}
            />
          </Box>
        </Body>
      </Container>

      <TotalizadoresFixos
        quantidadeItens={informacoesRodape.quantidadeItens}
        totalProdutos={informacoesRodape.totalProdutos}
        valorTotalProdutos={informacoesRodape.valorTotalProdutos}
      />

      <Box
        position="fixed"
        bottom="0"
        left="0"
        right="0"
        bg="gray.50"
        borderTop="1px solid"
        borderColor="#5502B2"
        py="16px"
        px="48px"
        zIndex={999} // Menor que os totalizadores (1000)
        pt="96px" // Espaço para os totalizadores (80px + 16px de margem)
      >
        <Flex justifyContent="space-between" align="center">
          <Button
            variant="outlineDefault"
            borderRadius="full"
            w="full"
            maxW={{ base: 'full', md: '160px' }}
            onClick={handleVoltar}
          >
            Voltar
          </Button>
          <Stack
            w="full"
            justifyContent="flex-end"
            direction={{ base: 'column', md: 'row' }}
            spacing={{ base: 2, sm: 4, md: 6 }}
          >
            {isReadOnly ? (
              <Button
                variant="outlineDefault"
                borderRadius="full"
                w="full"
                maxW={{ base: 'full', md: '196px' }}
                onClick={voltarParaListagem}
              >
                Voltar para a listagem
              </Button>
            ) : (
              <Button
                variant="outlineDefault"
                borderRadius="full"
                w="full"
                maxW={{ base: 'full', md: '160px' }}
                onClick={handleDescartarEntradaMercadoria}
                isDisabled={!temPermissaoExcluir}
              >
                Descartar
              </Button>
            )}
            {!isReadOnly && (
              <Button
                variant="outlineDefault"
                borderRadius="full"
                w="full"
                maxW={{ base: 'full', md: '160px' }}
                onClick={handleSalvarRascunho}
              >
                Salvar e sair
              </Button>
            )}
            <Button
              colorScheme="purple"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '160px' }}
              onClick={handleAvancar}
              isDisabled={!todosProdutosVinculados}
            >
              Avançar
            </Button>
          </Stack>
        </Flex>
      </Box>
    </>
  );
}
