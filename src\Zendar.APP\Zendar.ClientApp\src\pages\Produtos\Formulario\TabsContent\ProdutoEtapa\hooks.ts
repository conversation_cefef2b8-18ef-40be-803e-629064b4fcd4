import { toast } from 'react-toastify';
import api, { ResponseApi } from 'services/api';
import {
  useCallback,
  useEffect,
  useRef,
  useState,
  TouchEvent,
  DragEvent,
  useImperativeHandle,
} from 'react';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { useProdutosFormularioContext } from 'store/Produtos/ProdutosFormulario';

import ModalEtapaCadastrar from './components/ModalEtapa/Cadastrar';
import ModalEtapaAlterar from './components/ModalEtapa/Alterar';

export type EtapasProps = {
  id: string;
  categoria: string;
  tipoEtapa: number;
  quantidade: number;
  textoParaExibicao?: string;
  quantidadeMaxima?: number;
  telasExibicao: number[];
};

type SequenceStepProps = {
  produtoEtapaId: string;
  sequenciaOrdenacao: number;
};

export const FILTRO_TODAS_ETAPAS = 0;
export const FILTRO_FRENTE_DE_CAIXA = 7;
export const telasFrenteDeCaixa = [1, 2, 4];

const useProdutoEtapa = () => {
  const { idProduto, readonly, refSalvarProduto } =
    useProdutosFormularioContext();

  const [isLoading, setIsLoading] = useState(false);
  const [listaEtapasOriginal, setListaEtapasOriginal] = useState<EtapasProps[]>(
    []
  );
  const [listaEtapasFiltrada, setListaEtapasFiltrada] =
    useState<EtapasProps[]>(listaEtapasOriginal);
  const [valorFiltroAtivo, setValorFiltroAtivo] = useState(FILTRO_TODAS_ETAPAS);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOverItemIndex, setDragOverItemIndex] = useState<
    number | undefined
  >(undefined);

  const itemDraggableRef = useRef<HTMLElement | null>(null);
  const initialTouchPosition = useRef({ x: 0, y: 0 });

  function verificarSeEtapasPossuemTelasExibicaoIguais(
    arr1: number[],
    arr2: number[]
  ) {
    if (arr1.length !== arr2.length) return false;
    return arr1.every((valor, index) => valor === arr2[index]);
  }

  function verificarTelasExibicao(arrayTelas: EtapasProps[]) {
    if (arrayTelas.length === 0) return false;

    const primeiraTelaExibicao = arrayTelas[0].telasExibicao;

    const temDiferente = arrayTelas
      .slice(1)
      .some(
        (item) =>
          !verificarSeEtapasPossuemTelasExibicaoIguais(
            primeiraTelaExibicao,
            item.telasExibicao
          )
      );

    return temDiferente;
  }

  const exibirFiltro = verificarTelasExibicao(listaEtapasOriginal);
  const podeArrastarEtapas =
    !exibirFiltro || valorFiltroAtivo === FILTRO_TODAS_ETAPAS;

  const aplicarFiltro = (tela: number) => {
    if (tela === FILTRO_FRENTE_DE_CAIXA) {
      const etapasFrenteDeCaixa = listaEtapasOriginal.filter((etapa) =>
        etapa.telasExibicao.some((telaExibicao) =>
          telasFrenteDeCaixa.includes(telaExibicao)
        )
      );
      setValorFiltroAtivo(FILTRO_FRENTE_DE_CAIXA);
      setListaEtapasFiltrada(etapasFrenteDeCaixa);
      return;
    }

    if (tela === FILTRO_TODAS_ETAPAS) {
      setListaEtapasFiltrada(listaEtapasOriginal);
      setValorFiltroAtivo(FILTRO_TODAS_ETAPAS);
      return;
    }

    const listaFiltrada = listaEtapasOriginal.filter((etapa) =>
      etapa.telasExibicao.includes(tela)
    );
    setListaEtapasFiltrada(listaFiltrada);
    setValorFiltroAtivo(tela);
  };

  const opcoesLocaisExibicao: { [key: number]: string } = {
    1: 'Frente Caixa',
    2: 'Frente Caixa',
    4: 'Frente Caixa',
    7: 'Frente Caixa',
    8: 'Comanda Garçom',
    256: 'Auto Atendimento',
    1024: 'Cardápio Digital',
  };

  const obterOndeSeraExibidoEtapa = (
    locaisQuePodemSerExibidos: number[]
  ): string => {
    const valoresAplicativos = [1, 2, 4, 8, 256, 1024];
    const exibirEmTodosOsLocais = valoresAplicativos.every((valor) =>
      locaisQuePodemSerExibidos.includes(valor)
    );

    if (exibirEmTodosOsLocais) {
      return 'Todos';
    }

    const locais = new Set();

    locaisQuePodemSerExibidos.forEach((local) => {
      if (opcoesLocaisExibicao[local]) {
        locais.add(opcoesLocaisExibicao[local]);
      }
    });

    return Array.from(locais).join(', ');
  };

  const opcoesFiltro = () => {
    const telasUnicas = [
      ...new Set(listaEtapasOriginal.flatMap((item) => item.telasExibicao)),
    ];

    const resultado = [
      {
        label: 'Todas as etapas',
        value: FILTRO_TODAS_ETAPAS,
      },
    ] as {
      value: number;
      label: string;
    }[];

    telasUnicas.forEach((tela) => {
      const label = opcoesLocaisExibicao[tela] || 'Todas as etapas';

      const valorExistente = resultado.some((item) => item.value === tela);

      if (telasFrenteDeCaixa.includes(tela)) {
        if (!resultado.some((item) => item.label === 'Frente de Caixa')) {
          resultado.push({
            label: 'Frente de Caixa',
            value: FILTRO_FRENTE_DE_CAIXA,
          });
        }
      } else if (!valorExistente) {
        resultado.push({ label, value: tela });
      }
    });

    return resultado;
  };

  const buscarEtapas = useCallback(async () => {
    setIsLoading(true);

    const response = await api.get<void, ResponseApi<EtapasProps[]>>(
      ConstanteEnderecoWebservice.PRODUTO_ETAPA.replace('id', idProduto)
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso && response?.dados) {
        setListaEtapasFiltrada(response.dados);
        setListaEtapasOriginal(response.dados);
      }
    }

    setIsLoading(false);
  }, [idProduto]);

  const handleDeleteStep = useCallback(
    async (stepId: string) => {
      const response = await api.delete<void, ResponseApi>(
        `${ConstanteEnderecoWebservice.PRODUTO_ETAPA.replace(
          'id',
          idProduto
        )}/${stepId}`
      );

      if (response) {
        if (response?.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }

        if (response?.sucesso) {
          toast.success('Etapa removida com sucesso!');
          await buscarEtapas();
        }
      }
    },
    [buscarEtapas, idProduto]
  );

  const handleRegisterStep = useCallback(async () => {
    ModalEtapaCadastrar({
      callback: async (data) => {
        const response = await api.post<void, ResponseApi>(
          ConstanteEnderecoWebservice.PRODUTO_ETAPA.replace('id', idProduto),
          {
            ...data,
          }
        );

        if (response) {
          if (response?.avisos) {
            response.avisos.forEach((aviso) => toast.warning(aviso));
          }

          if (response?.sucesso) {
            toast.success('Etapa adicionada com sucesso!');
            await buscarEtapas();

            return { success: true };
          }
        }

        return { success: false };
      },
    });
  }, [buscarEtapas, idProduto]);

  const handleUpdateStep = useCallback(
    (stepId: string) => {
      ModalEtapaAlterar({
        stepId,
        productId: idProduto,
        callback: async (data) => {
          const response = await api.put<void, ResponseApi>(
            `${ConstanteEnderecoWebservice.PRODUTO_ETAPA.replace(
              'id',
              idProduto
            )}/${stepId}`,
            {
              ...data,
            }
          );

          if (response) {
            if (response?.avisos) {
              response.avisos.forEach((aviso) => toast.warning(aviso));
            }

            if (response?.sucesso) {
              toast.success('Etapa alterada com sucesso');
              await buscarEtapas();

              return { success: true };
            }
          }

          return { success: false };
        },
      });
    },
    [buscarEtapas, idProduto]
  );

  const handleUpdateSequenceStep = useCallback(
    async (data: SequenceStepProps[]) => {
      const response = await api.put<void, ResponseApi>(
        ConstanteEnderecoWebservice.PRODUTO_ETAPA_SEQUENCIA_ORDENACAO.replace(
          'id',
          idProduto
        ),
        [...data]
      );

      if (response) {
        if (response?.avisos) {
          response.avisos.forEach((aviso) => toast.warning(aviso));
        }

        if (response?.sucesso) {
          return { success: true };
        }
      }

      return { success: false };
    },
    [idProduto]
  );

  const updateStepPosition = useCallback(
    (oldPosition: number) => {
      const newPosition = dragOverItemIndex;
      const isSamePosition = oldPosition === newPosition;

      if (newPosition === undefined || isSamePosition) {
        return undefined;
      }

      const newSteps = [...listaEtapasOriginal];
      const stepItem = listaEtapasOriginal[oldPosition];

      newSteps.splice(oldPosition, 1);
      newSteps.splice(newPosition, 0, stepItem);

      setListaEtapasOriginal(newSteps);
      setListaEtapasFiltrada(newSteps);
      return newSteps;
    },
    [dragOverItemIndex, listaEtapasOriginal]
  );

  const onStartDrag = useCallback(
    (event: DragEvent<HTMLDivElement> | TouchEvent<HTMLDivElement>) => {
      const cloneElement = event.currentTarget.cloneNode(true) as HTMLElement;

      cloneElement.style.width = `${event.currentTarget.clientWidth}px`;
      cloneElement.style.height = '50px';
      cloneElement.style.position = 'absolute';
      cloneElement.style.pointerEvents = 'none';
      cloneElement.style.border = '1px solid #E8E8E8';
      cloneElement.style.backgroundColor = 'white';
      cloneElement.style.display = 'flex';
      cloneElement.style.top = `${
        event.currentTarget.offsetTop + event.currentTarget.clientHeight / 2
      }px`;

      event.currentTarget.parentElement?.appendChild(cloneElement);

      itemDraggableRef.current = cloneElement;
    },
    []
  );

  const handleDragStart = useCallback(
    (event: DragEvent<HTMLDivElement>) => {
      setIsDragging(true);

      const image = new Image();
      event.dataTransfer.setDragImage(image, 0, 0);

      onStartDrag(event);
    },
    [onStartDrag]
  );

  const handleTouchStart = useCallback((event: TouchEvent<HTMLDivElement>) => {
    setIsDragging(true);

    const touch = event.touches[0];

    initialTouchPosition.current = {
      x: touch.clientX,
      y: touch.clientY,
    };
  }, []);

  const onDrag = useCallback(
    (event: DragEvent<HTMLDivElement> | TouchEvent<HTMLDivElement>) => {
      const cloneElement = itemDraggableRef.current;

      if (!cloneElement) {
        return;
      }

      let clientY = 0;
      const parentClientY =
        event.currentTarget.parentElement?.getBoundingClientRect().y || 0;

      if ('touches' in event) {
        const touch = event.touches[0];
        clientY = touch.clientY - parentClientY;
      } else {
        clientY = event.clientY - parentClientY;
      }

      cloneElement.style.top = `${
        clientY + event.currentTarget.clientHeight / 2
      }px`;
    },
    []
  );

  const handleDragOver = useCallback(
    (event: DragEvent<HTMLDivElement>, stepIndex: number) => {
      setDragOverItemIndex(stepIndex);
    },
    []
  );

  const handleTouchMove = useCallback(
    (event: TouchEvent<HTMLDivElement>, index: number) => {
      const touch = event.touches[0];

      if (itemDraggableRef.current === null) {
        onStartDrag(event);
      }

      const newPosition =
        index + (touch.clientY - initialTouchPosition.current.y) / 50 || 0;

      setDragOverItemIndex(
        Math.min(
          Math.ceil(newPosition > 0 ? newPosition : 0),
          listaEtapasOriginal.length - 1
        )
      );

      onDrag(event);
    },
    [initialTouchPosition, listaEtapasOriginal, onDrag, onStartDrag]
  );

  const handleDrag = useCallback(
    (event: DragEvent<HTMLDivElement>) => {
      onDrag(event);
    },
    [onDrag]
  );

  const handleDragEnd = useCallback(
    async (
      event: DragEvent<HTMLDivElement> | TouchEvent<HTMLDivElement>,
      stepIndex: number
    ) => {
      setIsLoading(true);
      setIsDragging(false);
      setDragOverItemIndex(undefined);

      initialTouchPosition.current = { x: 0, y: 0 };

      const cloneElement = itemDraggableRef.current;

      if (cloneElement) {
        cloneElement.remove();
        itemDraggableRef.current = null;
      }

      const newListSteps = updateStepPosition(stepIndex);

      if (!newListSteps) {
        setIsLoading(false);
        return;
      }

      const { success } = await handleUpdateSequenceStep(
        newListSteps.map((step, index) => ({
          produtoEtapaId: step.id,
          sequenciaOrdenacao: index + 1,
        }))
      );

      if (success) {
        toast.success('Sequência da etapa alterada com sucesso');
        setIsLoading(false);
        return;
      }

      await buscarEtapas();
    },
    [updateStepPosition, handleUpdateSequenceStep, buscarEtapas]
  );

  useImperativeHandle(refSalvarProduto, () => ({
    submitFormularioProduto: async () => ({ sucesso: true }),
  }));

  useEffect(() => {
    buscarEtapas();
  }, [buscarEtapas]);

  return {
    handleRegisterStep,
    handleUpdateStep,
    handleDeleteStep,
    handleDragStart,
    handleDragOver,
    handleDrag,
    handleDragEnd,
    dragOverItemIndex,
    handleTouchStart,
    handleTouchMove,
    isDragging,
    isLoading,
    readonly,
    obterOndeSeraExibidoEtapa,
    exibirFiltro,
    opcoesFiltro,
    opcoesLocaisExibicao,
    aplicarFiltro,
    listaEtapasFiltrada,
    valorFiltroAtivo,
    podeArrastarEtapas,
  };
};

export { useProdutoEtapa };
