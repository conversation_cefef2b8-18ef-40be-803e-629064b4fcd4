import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import { ResponseApi } from 'services/api';
import { sistemaApiKey } from 'services/sistemaApiKey';

import { usePdvAutonomoContext } from 'store/PdvAutonomo';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

type CodigoAtivacaoProps = {
  listCodigoAtivacao: string[];
  pin: string;
};

let codigoAtivacaoTimeout: NodeJS.Timeout;

export const useDados = () => {
  const { infoIntegracao } = usePdvAutonomoContext();

  const [isLoading, setIsLoading] = useState(false);
  const [codigoAtivacao, setCodigoAtivacao] = useState<CodigoAtivacaoProps>();
  const [codigoAtivacaoFoiCopiado, setCodigoAtivacaoFoiCopiado] =
    useState(false);

  const integracaoAtiva = infoIntegracao?.ativo;

  const obterPIN = useCallback(async () => {
    setIsLoading(true);

    const response = await sistemaApiKey().get<void, ResponseApi<string>>(
      ConstanteEnderecoWebservice.PDV_AUTONOMO_GERAR_PIN
    );

    if (response) {
      if (response?.avisos) {
        response.avisos.forEach((aviso) => toast.warning(aviso));
      }

      if (response?.sucesso) {
        const { dados } = response;

        setCodigoAtivacao({
          pin: dados,
          listCodigoAtivacao: Array.from(dados),
        });
      }
    }
    setIsLoading(false);
  }, []);

  const handleCopiarCodigoAtivacao = () => {
    if (!codigoAtivacao?.pin) {
      return;
    }

    clearTimeout(codigoAtivacaoTimeout);

    setCodigoAtivacaoFoiCopiado(true);
    navigator.clipboard.writeText(codigoAtivacao.pin);

    codigoAtivacaoTimeout = global.setTimeout(() => {
      setCodigoAtivacaoFoiCopiado(false);
    }, 2000);
  };

  useEffect(() => {
    if (integracaoAtiva) {
      obterPIN();
    }
  }, [integracaoAtiva, obterPIN]);

  return {
    codigoAtivacao,
    isLoading,
    codigoAtivacaoFoiCopiado,
    handleCopiarCodigoAtivacao,
  };
};
