import React from 'react';
import { Flex } from '@chakra-ui/react';

import { PagedTable } from 'components/update/Table/PagedTable';
import { PaginationData } from 'components/update/Pagination';

import { VariacaoItem, VariacoesResponseProps } from '../../VariacaoItem';
import { EnumTelaConsultaProdutosPdv } from '..';

interface ConsultaVariacoesProps {
  tela: EnumTelaConsultaProdutosPdv;
  paginationHandleGrade: (
    paginationData: PaginationData,
    produtoId?: string
  ) => Promise<void>;
  totalRegistrosGrade: number;
  listarVariacoes: VariacoesResponseProps[];
  casasDecimais: {
    casasDecimaisQuantidade: number;
  };
  toggleSelectVariacao: (variacao: VariacoesResponseProps) => void;
  variacaoSelecionada: VariacoesResponseProps | null;
  handleDoubleClickVariacao: (variacao: VariacoesResponseProps) => void;
}

export const ConsultaVariacoes = ({
  tela,
  paginationHandleGrade,
  totalRegistrosGrade,
  listarVariacoes,
  casasDecimais,
  toggleSelectVariacao,
  variacaoSelecionada,
  handleDoubleClickVariacao,
}: ConsultaVariacoesProps) => {
  if (tela !== EnumTelaConsultaProdutosPdv.VARIACOES) {
    return null;
  }

  return (
    <PagedTable
      variant="card"
      bg="transparent"
      boxShadow="none"
      sx={{
        '& div': {
          padding: '0px 2px',
        },
      }}
      paginationHasDivider={false}
      loadColumnsData={paginationHandleGrade}
      itensPerPage={25}
      itemsTotalCount={totalRegistrosGrade}
      defaultKeyOrdered="nome"
      tableHeaders={[
        {
          key: 'grade',
          content: 'Grade de variações',
          isOrderable: false,
          pl: '58px !important',
        },
        {
          content: (
            <Flex alignItems="center" justifyContent="flex-end">
              Preço de venda
            </Flex>
          ),
          key: 'PrecoVenda',
          isOrderable: false,
        },
        {
          content: (
            <Flex alignItems="center" justifyContent="flex-end">
              Estoque
            </Flex>
          ),
          key: 'Estoque',
          isOrderable: false,
        },
      ]}
      renderTableRows={(listarVariacoes || []).map((variacao) => (
        <VariacaoItem
          variacao={variacao}
          casasDecimais={casasDecimais}
          toggleSelectVariacao={toggleSelectVariacao}
          variacaoSelecionada={variacaoSelecionada}
          handleDoubleClickVariacao={handleDoubleClickVariacao}
        />
      ))}
    />
  );
};
