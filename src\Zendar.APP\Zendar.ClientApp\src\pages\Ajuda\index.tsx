import {
  Flex,
  Grid,
  GridItem,
  Icon,
  Text,
  useMediaQuery,
} from '@chakra-ui/react';

import TipoSistemaEnum from 'constants/enum/tipoSistema';
import auth from 'modules/auth';
import { pushExternalUrl } from 'helpers/layout/pushExternalUrl';

import { SuporteTecnicoRevenda } from './SuporteTecnicoRevenda';
import { SuporteTecnicoSTi3 } from './SuporteTecnicoSTi3';
import { AreaArtigoIcon, AreaVideosIcon } from 'icons';
import {
  gerarTreinamentosAccessToken,
  treinamentosBaseUrl,
} from 'services/treinamentos';

const RotasTreinamentos = {
  LOGIN: '/login/',
  INICIO: '/home',
};

export const Ajuda = ({ exibirChat = true }: { exibirChat: boolean }) => {
  const nomeUsuario = auth.getUsuario()?.nome;
  const sistema = auth.getSistema();
  const isClienteSTi3 =
    sistema.value === TipoSistemaEnum.ZENDAR ||
    sistema.value === TipoSistemaEnum.FOMER ||
    sistema.value === TipoSistemaEnum.FOMER_SISTEMA;
  function handlePushCentralAjuda() {
    pushExternalUrl('https://treinamentosdesistema.freshdesk.com/support/home');
  }

  const adicionarTokenRotaTreinamentos = async (win: Window | null) => {
    const response = await gerarTreinamentosAccessToken();

    if (!win) {
      return;
    }

    if (!response?.sucesso) {
      return;
    }

    const rotaTreinamentosComToken = `${treinamentosBaseUrl}${RotasTreinamentos.LOGIN}${response.dados}`;

    win.location.href = rotaTreinamentosComToken;
    win.focus();
  };

  const handleRedirecionarParaTreinamentos = () => {
    const win = window.open(
      '',
      `${treinamentosBaseUrl}${RotasTreinamentos.INICIO}`
    );

    adicionarTokenRotaTreinamentos(win);
  };

  return (
    <Flex position="relative" justify="center" flexDir="column" align="center">
      <Flex
        justify="center"
        background="ajuda.bg"
        align="flex-start"
        pt="24px"
        h="240px"
        w="100%"
        top="0px"
        position="absolute"
      />
      <Text
        fontSize="20px"
        color="white"
        zIndex={1}
        mt="24px"
        textAlign={['center', 'start']}
      >
        Olá{' '}
        <Text as="span" color="secondary.300">
          {nomeUsuario}
        </Text>
        , como podemos ajudar?
      </Text>
      <Grid
        zIndex={1}
        templateRows={[
          'repeat(auto-fit, minmax(120px, 120px))',
          'repeat(1, minmax(180px, 180px))',
        ]}
        templateColumns={[
          'repeat(auto-fit, minmax(120px, 120px))',
          'repeat(auto-fit, minmax(120px, 120px))',
          'repeat(3, minmax(280px, 280px))',
        ]}
        gap={4}
        pt="26px"
        h={['full', 'full', 'full', '312px']}
        w={['100%', '100%', 'calc(280px * 3 + 16px * 3)']}
        px={[4, 8, 4, 0]}
      >
        <GridItem
          rowSpan={2}
          colSpan={[4, 6, 3, 1]}
          bg="white"
          height={['fit-content', 'fit-content', 'fit-content', '280px']}
          borderRadius="8px"
          boxShadow="0px 3px 6px #00000029"
        >
          <Flex flexDir="column" padding="28px 32px" gap="20px">
            <Icon as={AreaArtigoIcon} stroke="black" fontSize="24px" />
            <Text color="ajuda.titulo" fontSize="16px" fontWeight="bold">
              Base de conhecimento
            </Text>
            <Text
              color="gray.700"
              fontSize="14px"
              letterSpacing="0px"
              whiteSpace={['normal', 'normal', 'normal', 'pre-line']}
            >
              {`Encontre artigos e explicações \n técnicas para casos de uso, \n gestão e negócios em geral.`}
            </Text>

            <Text
              color="blue.700"
              fontSize="14px"
              onClick={handlePushCentralAjuda}
              textDecor="underline"
              cursor="pointer"
            >
              Pesquisar artigos →
            </Text>
          </Flex>
        </GridItem>
        <GridItem
          rowSpan={2}
          colSpan={[4, 6, 3, 1]}
          bg="white"
          height={['fit-content', 'fit-content', 'fit-content', '280px']}
          borderRadius="8px"
          boxShadow="0px 3px 6px #00000029"
        >
          <Flex flexDir="column" padding="28px 32px" gap="20px">
            <Icon as={AreaVideosIcon} color="primary.300" fontSize="24px" />
            <Text color="ajuda.titulo" fontSize="16px" fontWeight="bold">
              Treinamentos e Tutoriais
            </Text>
            <Text
              color="gray.700"
              fontSize="14px"
              letterSpacing="0px"
              whiteSpace={['normal', 'normal', 'normal', 'pre-line']}
            >
              {`Videos explicativos e tutoriais\n de uso para os principais\n recursos do sistema.`}
            </Text>

            <Text
              color="blue.700"
              fontSize="14px"
              textDecor="underline"
              cursor="pointer"
              onClick={handleRedirecionarParaTreinamentos}
            >
              Acessar vídeos tutoriais →
            </Text>
          </Flex>
        </GridItem>
        {isClienteSTi3 ? (
          <SuporteTecnicoSTi3 exibirChat={exibirChat} />
        ) : (
          <SuporteTecnicoRevenda />
        )}
      </Grid>
    </Flex>
  );
};
